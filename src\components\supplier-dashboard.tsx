"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useAuth } from "@/lib/auth"
import { TransactionHistory } from "@/components/transaction-history"
import { ExchangeRateWidget } from "@/components/exchange-rate-widget"
import { Navigation } from "@/components/navigation"

export function SupplierDashboard() {
  const { user } = useAuth()

  // Mock data - in production this would come from your database
  const metrics = {
    totalRevenue: 125000,
    monthlyRevenue: 15000,
    totalTransactions: 1250,
    monthlyTransactions: 180,
    averageTransactionValue: 100,
    profitMargin: 12.5,
    activeOperators: 8,
    pendingPayouts: 2500,
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="border-primary/20">
                <CardHeader className="pb-3">
                  <CardDescription>Total Revenue</CardDescription>
                  <CardTitle className="text-2xl text-primary">${metrics.totalRevenue.toLocaleString()}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground">
                      +{((metrics.monthlyRevenue / metrics.totalRevenue) * 100).toFixed(1)}%
                    </Badge>
                    <span className="text-sm text-muted-foreground">this month</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-primary/20">
                <CardHeader className="pb-3">
                  <CardDescription>Monthly Revenue</CardDescription>
                  <CardTitle className="text-2xl text-secondary">${metrics.monthlyRevenue.toLocaleString()}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Progress value={75} className="h-2" />
                  <p className="text-sm text-muted-foreground mt-2">75% of monthly goal</p>
                </CardContent>
              </Card>

              <Card className="border-primary/20">
                <CardHeader className="pb-3">
                  <CardDescription>Total Transactions</CardDescription>
                  <CardTitle className="text-2xl text-accent">{metrics.totalTransactions.toLocaleString()}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-accent/20 text-accent-foreground">
                      {metrics.monthlyTransactions}
                    </Badge>
                    <span className="text-sm text-muted-foreground">this month</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-primary/20">
                <CardHeader className="pb-3">
                  <CardDescription>Profit Margin</CardDescription>
                  <CardTitle className="text-2xl text-primary">{metrics.profitMargin}%</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-primary/20 text-primary-foreground">
                      Avg: ${metrics.averageTransactionValue}
                    </Badge>
                    <span className="text-sm text-muted-foreground">per transaction</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Transaction History */}
            <TransactionHistory />
          </div>

          {/* Right Column - Exchange Rates */}
          <div className="space-y-6">
            <ExchangeRateWidget compact={true} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default SupplierDashboard