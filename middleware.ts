// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req: request, res })

  // Get the user's session
  const {
    data: { session },
  } = await supabase.auth.getSession()

  const { pathname } = request.nextUrl

  // Define protected routes and their required roles
  const protectedRoutes = {
    '/operator': ['Operator'],
    '/supplier': ['Supplier'],
  }

  // Check if the current path is a protected route
  const isProtectedRoute = Object.keys(protectedRoutes).some(route => 
    pathname.startsWith(route)
  )

  if (isProtectedRoute) {
    // If no session, redirect to login
    if (!session) {
      const loginUrl = new URL('/login', request.url)
      return NextResponse.redirect(loginUrl)
    }

    // Get user role from users table
    const { data: userData, error } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (error || !userData) {
      console.error('Error fetching user role:', error)
      const loginUrl = new URL('/login', request.url)
      return NextResponse.redirect(loginUrl)
    }

    // Check if user has required role for the route
    const requiredRoles = protectedRoutes[/operator/.test(pathname) ? '/operator' : '/supplier']
    
    if (!requiredRoles.includes(userData.role)) {
      // User doesn't have required role, redirect to unauthorized or appropriate dashboard
      const redirectPath = userData.role === 'Supplier' ? '/supplier' : '/operator'
      const redirectUrl = new URL(redirectPath, request.url)
      return NextResponse.redirect(redirectUrl)
    }
  }

  // If user is logged in and tries to access login page, redirect to appropriate dashboard
  if (pathname === '/login' && session) {
    // Get user role to determine where to redirect
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    const redirectPath = userData?.role === 'Supplier' ? '/supplier' : '/operator'
    const redirectUrl = new URL(redirectPath, request.url)
    return NextResponse.redirect(redirectUrl)
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}