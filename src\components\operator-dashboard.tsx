"use client"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/lib/auth"
import { TransactionHistory } from "@/components/transaction-history"
import TransactionForm from "@/components/transaction-form"
import { ExchangeRateWidget } from "@/components/exchange-rate-widget"
import CurrencyConverter from "@/components/currency-converter"
import Navigation from "@/components/navigation"

export function OperatorDashboard() {
  const { user } = useAuth()

  // Mock data - in production this would come from your database
  const metrics = {
    dailyTransactions: 25,
    dailyVolume: 2500,
    commission: 125,
    availableBalance: 5000,
    exchangeRates: {
      USDC: 1.0,
      USDT: 1.0,
      BUSD: 1.0,
    },
  }

  const recentTransactions = [
    { id: "1", amount: 100, currency: "USDC", type: "buy", status: "completed", timestamp: "2024-01-15 14:30" },
    { id: "2", amount: 50, currency: "USDT", type: "sell", status: "completed", timestamp: "2024-01-15 13:45" },
    { id: "3", amount: 200, currency: "USDC", type: "buy", status: "pending", timestamp: "2024-01-15 12:20" },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <Navigation />

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Metrics and History */}
          <div className="lg:col-span-2 space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-primary/20">
                <CardHeader className="pb-3">
                  <CardDescription>Daily Transactions</CardDescription>
                  <CardTitle className="text-2xl text-primary">{metrics.dailyTransactions}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary" className="bg-secondary/20 text-secondary-foreground">
                    Volume: ${metrics.dailyVolume}
                  </Badge>
                </CardContent>
              </Card>

              <Card className="border-primary/20">
                <CardHeader className="pb-3">
                  <CardDescription>Commission Earned</CardDescription>
                  <CardTitle className="text-2xl text-secondary">${metrics.commission}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Badge variant="secondary" className="bg-accent/20 text-accent-foreground">
                    Available: ${metrics.availableBalance}
                  </Badge>
                </CardContent>
              </Card>
            </div>

            {/* Exchange Rates */}
            <ExchangeRateWidget />

            {/* Currency Converter */}
            <CurrencyConverter />

            {/* Recent Transactions */}
            <Card className="border-primary/20">
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Your latest transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentTransactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-4 border border-primary/10 rounded-lg"
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`w-10 h-10 rounded-full flex items-center justify-center ${transaction.type === "buy" ? "bg-primary/10" : "bg-secondary/10"
                            }`}
                        >
                          <span
                            className={`text-sm font-medium ${transaction.type === "buy" ? "text-primary" : "text-secondary"
                              }`}
                          >
                            {transaction.type === "buy" ? "↓" : "↑"}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium capitalize">
                            {transaction.type} {transaction.currency}
                          </p>
                          <p className="text-sm text-muted-foreground">{transaction.timestamp}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${transaction.amount}</p>
                        <Badge
                          variant={transaction.status === "completed" ? "default" : "secondary"}
                          className={transaction.status === "completed" ? "bg-primary text-primary-foreground" : ""}
                        >
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Transaction History */}
            <TransactionHistory />
          </div>

          {/* Right Column - Transaction Form */}
          <div className="space-y-6">
            {/* Transaction Form */}
            <TransactionForm />
          </div>
        </div>
      </div>
    </div>
  )
}

export default OperatorDashboard