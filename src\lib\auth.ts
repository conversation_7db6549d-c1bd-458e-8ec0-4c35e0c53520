// app/lib/auth.ts
import { supabase } from "@/utils/supabaseClient";

export interface User {
    id: string;
    email: string;
    nickname: string;
    role: "supplier" | "operator";
    wallet_address?: string;
    created_at: string;
}

export interface Transaction {
    id: string;
    type: "supplier_to_operator" | "operator_to_customer" | "customer_to_operator" | "settlement";
    usdt_amount: number;
    mwk_amount: number;
    rate_used: number;
    txid?: string;
    counterparty: string;
    timestamp: string;
    user_id: string;
}

export interface Profit {
    id: string;
    week_id: string;
    total_usdt_received: number;
    total_mwk_collected: number;
    gross_profit: number;
    supplier_share: number;
    operator_share: number;
    timestamp: string;
}
export interface ExchangeRates {
    usd_mwk: number;
    usd_zar: number;
    black_market_rate: number;
    last_updated: string;
    source: string;
}

export interface USDTBalance {
    balance: number;
    address: string;
    trx_balance: number;
    last_updated: string;
}
export const getCurrentUser = async (): Promise<User | null> => {
    try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) return null

        const { data: userRow, error } = await supabase
            .from("users")
            .select("*")
            .eq("id", user.id)
            .single()

        if (error || !userRow) {
            console.error("User not found in database:", error)
            return null
        }

        return userRow
    } catch (error) {
        console.error("Error getting current user:", error)
        return null
    }
}

export const logout = async () => {
    try {
        const { error } = await supabase.auth.signOut()
        if (error) throw error
    } catch (error) {
        console.error("Logout error:", error)
        throw error
    }
}

export const getTransactions = async (): Promise<Transaction[]> => {
    const user = await getCurrentUser();
    if (!user) return [];

    const { data, error } = await supabase
        .from("transactions")
        .select("*")
        .eq("user_id", user.id)
        .order("timestamp", { ascending: false });

    if (error) {
        console.error("Error fetching transactions:", error);
        return [];
    }

    return data || [];
};

export const addTransaction = async (transaction: Omit<Transaction, "id" | "timestamp">): Promise<Transaction | null> => {
    const newTransaction = {
        ...transaction,
        timestamp: new Date().toISOString(),
    };

    const { data, error } = await supabase
        .from("transactions")
        .insert([newTransaction])
        .select()
        .single();

    if (error) {
        console.error("Error adding transaction:", error);
        return null;
    }

    return data;
};

export const getProfits = async (): Promise<Profit[]> => {
    const user = await getCurrentUser();
    if (!user) return [];

    const { data, error } = await supabase
        .from("profits")
        .select("*")
        .eq("user_id", user.id)
        .order("timestamp", { ascending: false });

    if (error) {
        console.error("Error fetching profits:", error);
        return [];
    }

    return data || [];
};

// Real API integration for exchange rates
import { getExchangeRates as getRealExchangeRates } from "./exchange-rates";
import { getTronWalletInfo } from "./web3";

export const getExchangeRates = async () => {
    try {
        // Mock data for now - replace with actual API call
        return {
            usd_mwk: 1750,
            black_market_rate: 1800,
            usd_zar: 18.5
        }
    } catch (error) {
        console.error("Error fetching exchange rates:", error)
        return null
    }
}

export const getUSDTBalance = async () => {
    try {
        // Mock data for now - replace with actual wallet API call
        return {
            balance: 2450.50,
            trx_balance: 125.75
        }
    } catch (error) {
        console.error("Error fetching USDT balance:", error)
        return null
    }
}
