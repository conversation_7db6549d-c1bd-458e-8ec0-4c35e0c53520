export interface TransactionFees {
  supplierFee: number
  operatorCommission: number
  networkFee: number
  totalFees: number
}

export interface ProfitCalculation {
  grossProfit: number
  netProfit: number
  profitMargin: number
  roi: number
}

export interface CashFlowMetrics {
  dailyCashFlow: number
  weeklyCashFlow: number
  monthlyCashFlow: number
  projectedAnnualFlow: number
}

export interface RiskMetrics {
  volatilityRisk: number
  liquidityRisk: number
  operationalRisk: number
  overallRiskScore: number
}

export class FinancialCalculationsEngine {
  // Fee structure configuration
  private static readonly FEE_STRUCTURE = {
    supplierBaseFee: 0.005, // 0.5%
    operatorCommissionRate: 0.02, // 2%
    networkFeeFlat: 0.5, // $0.50 flat fee
    networkFeePercentage: 0.001, // 0.1%
    minimumTransaction: 10,
    maximumTransaction: 10000,
  }

  // Exchange rate spreads for different currencies
  private static readonly CURRENCY_SPREADS = {
    USDC: 0.001, // 0.1% spread
    USDT: 0.0015, // 0.15% spread
    BUSD: 0.0012, // 0.12% spread
    DAI: 0.0008, // 0.08% spread
  }

  /**
   * Calculate transaction fees breakdown
   */
  static calculateTransactionFees(
    amount: number,
    currency: keyof typeof this.CURRENCY_SPREADS = "USDC",
  ): TransactionFees {
    const supplierFee = amount * this.FEE_STRUCTURE.supplierBaseFee
    const operatorCommission = amount * this.FEE_STRUCTURE.operatorCommissionRate
    const networkFeePercentage = amount * this.FEE_STRUCTURE.networkFeePercentage
    const networkFee = this.FEE_STRUCTURE.networkFeeFlat + networkFeePercentage

    // Add currency spread
    const currencySpread = amount * this.CURRENCY_SPREADS[currency]

    const totalFees = supplierFee + operatorCommission + networkFee + currencySpread

    return {
      supplierFee: Number(supplierFee.toFixed(2)),
      operatorCommission: Number(operatorCommission.toFixed(2)),
      networkFee: Number(networkFee.toFixed(2)),
      totalFees: Number(totalFees.toFixed(2)),
    }
  }

  /**
   * Calculate profit metrics for suppliers
   */
  static calculateSupplierProfit(
    totalRevenue: number,
    totalCosts: number,
    totalTransactions: number,
  ): ProfitCalculation {
    const grossProfit = totalRevenue - totalCosts
    const netProfit = grossProfit * 0.85 // Account for taxes and other expenses
    const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0
    const roi = totalCosts > 0 ? (netProfit / totalCosts) * 100 : 0

    return {
      grossProfit: Number(grossProfit.toFixed(2)),
      netProfit: Number(netProfit.toFixed(2)),
      profitMargin: Number(profitMargin.toFixed(2)),
      roi: Number(roi.toFixed(2)),
    }
  }

  /**
   * Calculate operator commission and earnings
   */
  static calculateOperatorEarnings(
    transactionVolume: number,
    numberOfTransactions: number,
    performanceBonus = 0,
  ): {
    baseCommission: number
    volumeBonus: number
    performanceBonus: number
    totalEarnings: number
    averagePerTransaction: number
  } {
    const baseCommission = transactionVolume * this.FEE_STRUCTURE.operatorCommissionRate

    // Volume bonus: additional 0.1% for volumes over $10,000
    const volumeBonus = transactionVolume > 10000 ? (transactionVolume - 10000) * 0.001 : 0

    const totalEarnings = baseCommission + volumeBonus + performanceBonus
    const averagePerTransaction = numberOfTransactions > 0 ? totalEarnings / numberOfTransactions : 0

    return {
      baseCommission: Number(baseCommission.toFixed(2)),
      volumeBonus: Number(volumeBonus.toFixed(2)),
      performanceBonus: Number(performanceBonus.toFixed(2)),
      totalEarnings: Number(totalEarnings.toFixed(2)),
      averagePerTransaction: Number(averagePerTransaction.toFixed(2)),
    }
  }

  /**
   * Calculate cash flow metrics
   */
  static calculateCashFlow(
    transactions: Array<{
      amount: number
      timestamp: Date
      type: "inflow" | "outflow"
    }>,
  ): CashFlowMetrics {
    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    const calculateNetFlow = (fromDate: Date) => {
      return transactions
        .filter((t) => t.timestamp >= fromDate)
        .reduce((sum, t) => {
          return sum + (t.type === "inflow" ? t.amount : -t.amount)
        }, 0)
    }

    const dailyCashFlow = calculateNetFlow(oneDayAgo)
    const weeklyCashFlow = calculateNetFlow(oneWeekAgo)
    const monthlyCashFlow = calculateNetFlow(oneMonthAgo)

    // Project annual flow based on monthly trend
    const projectedAnnualFlow = monthlyCashFlow * 12

    return {
      dailyCashFlow: Number(dailyCashFlow.toFixed(2)),
      weeklyCashFlow: Number(weeklyCashFlow.toFixed(2)),
      monthlyCashFlow: Number(monthlyCashFlow.toFixed(2)),
      projectedAnnualFlow: Number(projectedAnnualFlow.toFixed(2)),
    }
  }

  /**
   * Calculate risk assessment metrics
   */
  static calculateRiskMetrics(
    transactionHistory: Array<{
      amount: number
      timestamp: Date
      currency: string
    }>,
    operatorCount: number,
    averageTransactionSize: number,
  ): RiskMetrics {
    // Volatility risk based on transaction amount variance
    const amounts = transactionHistory.map((t) => t.amount)
    const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length
    const variance = amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length
    const volatilityRisk = Math.min((Math.sqrt(variance) / avgAmount) * 100, 100)

    // Liquidity risk based on transaction frequency and size
    const dailyTransactions = transactionHistory.filter(
      (t) => t.timestamp >= new Date(Date.now() - 24 * 60 * 60 * 1000),
    ).length
    const liquidityRisk = Math.max(0, 100 - dailyTransactions * 2) // Lower risk with more transactions

    // Operational risk based on operator concentration
    const operationalRisk = operatorCount < 5 ? 80 : Math.max(20, 100 - operatorCount * 5)

    // Overall risk score (weighted average)
    const overallRiskScore = volatilityRisk * 0.4 + liquidityRisk * 0.3 + operationalRisk * 0.3

    return {
      volatilityRisk: Number(volatilityRisk.toFixed(1)),
      liquidityRisk: Number(liquidityRisk.toFixed(1)),
      operationalRisk: Number(operationalRisk.toFixed(1)),
      overallRiskScore: Number(overallRiskScore.toFixed(1)),
    }
  }

  /**
   * Calculate optimal transaction pricing
   */
  static calculateOptimalPricing(
    marketRate: number,
    volume: number,
    competitorRates: number[] = [],
  ): {
    recommendedRate: number
    competitiveAdvantage: number
    profitMargin: number
  } {
    const baseSpread = 0.005 // 0.5% base spread
    const volumeDiscount = volume > 50000 ? 0.001 : 0 // Volume discount
    const competitorAvg =
      competitorRates.length > 0
        ? competitorRates.reduce((sum, rate) => sum + rate, 0) / competitorRates.length
        : marketRate + baseSpread

    const recommendedRate = marketRate + baseSpread - volumeDiscount
    const competitiveAdvantage = competitorAvg > 0 ? ((competitorAvg - recommendedRate) / competitorAvg) * 100 : 0
    const profitMargin = ((recommendedRate - marketRate) / recommendedRate) * 100

    return {
      recommendedRate: Number(recommendedRate.toFixed(4)),
      competitiveAdvantage: Number(competitiveAdvantage.toFixed(2)),
      profitMargin: Number(profitMargin.toFixed(2)),
    }
  }

  /**
   * Calculate break-even analysis
   */
  static calculateBreakEven(
    fixedCosts: number,
    variableCostPerTransaction: number,
    averageRevenuePerTransaction: number,
  ): {
    breakEvenTransactions: number
    breakEvenRevenue: number
    marginOfSafety: number
    currentTransactions?: number
  } {
    const contributionMargin = averageRevenuePerTransaction - variableCostPerTransaction
    const breakEvenTransactions = contributionMargin > 0 ? Math.ceil(fixedCosts / contributionMargin) : 0
    const breakEvenRevenue = breakEvenTransactions * averageRevenuePerTransaction

    // Margin of safety (assuming current performance)
    const marginOfSafety = contributionMargin > 0 ? (contributionMargin / averageRevenuePerTransaction) * 100 : 0

    return {
      breakEvenTransactions,
      breakEvenRevenue: Number(breakEvenRevenue.toFixed(2)),
      marginOfSafety: Number(marginOfSafety.toFixed(2)),
    }
  }

  /**
   * Validate transaction limits and compliance
   */
  static validateTransaction(
    amount: number,
    currency: string,
    userTier: "basic" | "premium" | "enterprise" = "basic",
  ): {
    isValid: boolean
    errors: string[]
    warnings: string[]
    adjustedAmount?: number
  } {
    const errors: string[] = []
    const warnings: string[] = []
    const adjustedAmount = amount

    // Basic validation
    if (amount < this.FEE_STRUCTURE.minimumTransaction) {
      errors.push(`Minimum transaction amount is $${this.FEE_STRUCTURE.minimumTransaction}`)
    }

    // Tier-based limits
    const tierLimits = {
      basic: 1000,
      premium: 5000,
      enterprise: 10000,
    }

    if (amount > tierLimits[userTier]) {
      if (userTier === "basic") {
        warnings.push(`Amount exceeds ${userTier} tier limit. Consider upgrading to premium.`)
      } else {
        errors.push(`Amount exceeds ${userTier} tier limit of $${tierLimits[userTier]}`)
      }
    }

    // Currency validation
    if (!this.CURRENCY_SPREADS.hasOwnProperty(currency)) {
      errors.push(`Unsupported currency: ${currency}`)
    }

    // AML compliance check (simplified)
    if (amount > 3000) {
      warnings.push("Transaction may require additional verification for AML compliance")
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      adjustedAmount: errors.length === 0 ? adjustedAmount : undefined,
    }
  }
}

export default FinancialCalculationsEngine
