// app/page.tsx
"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "@/utils/supabaseClient"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Wallet, TrendingUp, Shield, Globe } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState<string>("")
  const [password, setPassword] = useState<string>("")
  const [nickname, setNickname] = useState<string>("")
  const [role, setRole] = useState<"Supplier" | "Operator">("Operator")
  const [isLogin, setIsLogin] = useState<boolean>(true)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Auto-redirect if already logged in
  useEffect(() => {
    const checkSession = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const { data: userRow, error: userError } = await supabase
          .from("users")
          .select("role")
          .eq("id", user.id)
          .single()
        
        if (userError || !userRow?.role) {
          console.error("User role not found, creating user record...")
          // Try to create the user record
          const { error: insertError } = await supabase
            .from("users")
            .insert({
              id: user.id,
              email: user.email!,
              nick_name: user.user_metadata?.nickname || '',
              role: user.user_metadata?.role || 'Operator'
            })
          
          if (!insertError) {
            // Retry getting the role
            const { data: newUserRow } = await supabase
              .from("users")
              .select("role")
              .eq("id", user.id)
              .single()
            
            if (newUserRow?.role) {
              const redirectPath = newUserRow.role === "Supplier" ? "/supplier" : "/operator"
              router.push(redirectPath)
              return
            }
          }
          
          setError("User role not found. Please contact support.")
          return
        }
        
        const redirectPath = userRow.role === "Supplier" ? "/supplier" : "/operator"
        router.push(redirectPath)
      }
    }
    checkSession()
  }, [router])

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (isLogin) {
        // LOGIN FLOW
        const { data, error: authError } = await supabase.auth.signInWithPassword({
          email,
          password,
        })

        if (authError) {
          throw new Error(authError.message)
        }

        if (!data.user) {
          throw new Error("No user data returned")
        }

        // Fetch role from users table
        const { data: userRow, error: userError } = await supabase
          .from("users")
          .select("role")
          .eq("id", data.user.id)
          .single()

        if (userError) {
          console.warn("User role fetch failed:", userError)
          throw new Error("Unable to verify user role. Please try again.")
        }

        if (!userRow?.role) {
          throw new Error("User role not found. Please contact support.")
        }

        // Redirect based on role
        const redirectPath = userRow.role === "Supplier" ? "/supplier" : "/operator"
        router.push(redirectPath)

      } else {
        // REGISTER FLOW
        if (password.length < 6) {
          throw new Error("Password must be at least 6 characters long")
        }

        const { data, error: authError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              role,
              nickname // Add nickname here
            }
          }
        })

        if (authError) {
          throw new Error(authError.message)
        }

        if (!data.user) {
          throw new Error("Registration failed - no user created")
        }

        if (data.session) {
          // Immediate sign-in (confirmation disabled)
          const { data: userRow } = await supabase
            .from("users")
            .select("role")
            .eq("id", data.user.id)
            .single()
          if (userRow?.role) {
            router.push(userRow.role === "Supplier" ? "/supplier" : "/operator")
          } else {
            throw new Error("User role not found after registration.")
          }
        } else {
          // Email confirmation required
          alert("Registration successful! Please check your email to confirm your account before signing in.")
          setIsLogin(true)
          setEmail("")
          setPassword("")
        }
      }
    } catch (err) {
      console.error("Auth error:", err)
      const errorMessage = err instanceof Error ? err.message : "An unexpected error occurred"
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const clearError = () => {
    setError(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <Wallet className="w-6 h-6 text-primary-foreground" />
            </div>
            <h1 className="text-2xl font-bold text-foreground">TEVAVEND</h1>
          </div>
          <p className="text-muted-foreground">USDT Vending & Remittance Management</p>
        </div>

        <div className="grid grid-cols-3 gap-4 mb-8">
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-chart-1/10 rounded-lg flex items-center justify-center mx-auto">
              <TrendingUp className="w-6 h-6 text-chart-1" />
            </div>
            <p className="text-xs text-muted-foreground">Real-time Rates</p>
          </div>
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-chart-2/10 rounded-lg flex items-center justify-center mx-auto">
              <Shield className="w-6 h-6 text-chart-2" />
            </div>
            <p className="text-xs text-muted-foreground">Secure Ledger</p>
          </div>
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-chart-3/10 rounded-lg flex items-center justify-center mx-auto">
              <Globe className="w-6 h-6 text-chart-3" />
            </div>
            <p className="text-xs text-muted-foreground">Web3 Ready</p>
          </div>
        </div>

        {error && (
          <div className="bg-destructive/15 text-destructive p-3 rounded-md text-sm">
            <div className="flex justify-between items-center">
              <span>{error}</span>
              <button
                onClick={clearError}
                className="text-destructive hover:text-destructive/80"
              >
                ×
              </button>
            </div>
          </div>
        )}

        <Card className="border-border/50 shadow-lg">
          <CardHeader className="space-y-1">
            <Tabs value={isLogin ? "login" : "register"} onValueChange={(v) => {
              setIsLogin(v === "login")
              clearError()
            }}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="login">Sign In</TabsTrigger>
                <TabsTrigger value="register">Register</TabsTrigger>
              </TabsList>

              <TabsContent value="login" className="space-y-4 mt-4">
                <CardTitle>Welcome back</CardTitle>
                <CardDescription>Sign in to your TEVAVEND account</CardDescription>
              </TabsContent>

              <TabsContent value="register" className="space-y-4 mt-4">
                <CardTitle>Create account</CardTitle>
                <CardDescription>Join the TEVAVEND network</CardDescription>
              </TabsContent>
            </Tabs>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleAuth} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    clearError()
                  }}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nickname">Nickname</Label>
                <Input
                  id="nickname"
                  type="name"
                  placeholder="Enter your Nickname..."
                  value={nickname}
                  onChange={(e) => {
                    setNickname(e.target.value)
                    clearError()
                  }}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    clearError()
                  }}
                  required
                  disabled={loading}
                  minLength={isLogin ? undefined : 6}
                />
                {!isLogin && (
                  <p className="text-xs text-muted-foreground">
                    Password must be at least 6 characters long
                  </p>
                )}
              </div>

              {!isLogin && (
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select
                    value={role}
                    onValueChange={(value) => setRole(value as "Supplier" | "Operator")}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Operator">Operator</SelectItem>
                      <SelectItem value="Supplier">Supplier</SelectItem>
                    </SelectContent>
                  </Select>

                  <p className="text-xs text-muted-foreground">
                    {role === "Operator"
                      ? "Manage local sales and customer transactions"
                      : "Provide liquidity and track remittances"}
                  </p>
                </div>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                    Processing...
                  </>
                ) : isLogin ? "Sign In" : "Create Account"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <p className="text-center text-xs text-muted-foreground">Secure • Encrypted • Backed Up</p>
      </div>
    </div>
  )
}

