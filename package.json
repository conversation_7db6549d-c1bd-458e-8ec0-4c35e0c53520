{"name": "tevavend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:generate": "drizzle-kit generate --config=drizzle-config.ts", "db:migrate": "drizzle-kit migrate --config=drizzle-config.ts", "db:studio": "drizzle-kit studio --config=drizzle-config.ts", "db:seed": "tsx scripts/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^7.9.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "@types/pg": "^8.15.5", "@upstash/redis": "^1.35.3", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.5", "embla-carousel-react": "^8.6.0", "fs": "0.0.1-security", "geist": "^1.5.1", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "lucide-react": "^0.544.0", "next": "15.5.3", "next-themes": "^0.4.6", "openai": "^5.20.2", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "19.1.0", "react-day-picker": "^9.11.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.6", "recharts": "^2.15.4", "sonner": "^2.0.7", "stripe": "^18.5.0", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.2", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.5.3", "posthog-js": "^1.266.0", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.8", "typescript": "^5"}}