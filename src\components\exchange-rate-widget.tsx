"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { exchangeRateService, type ExchangeRate } from "@/lib/services/exchange-rate-services"

interface ExchangeRateWidgetProps {
  compact?: boolean
  showRefresh?: boolean
}

export function ExchangeRateWidget({ compact = false, showRefresh = true }: ExchangeRateWidgetProps) {
  const [rates, setRates] = useState<Record<string, ExchangeRate>>({})
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  useEffect(() => {
    const unsubscribe = exchangeRateService.subscribe((newRates) => {
      setRates(newRates)
      setLastUpdate(exchangeRateService.getLastUpdateTime())
    })

    return unsubscribe
  }, [])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await exchangeRateService.forceRefresh()
    } catch (error) {
      console.error("[v0] Failed to refresh rates:", error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const formatTime = (date: Date | null) => {
    if (!date) return "Never"
    return date.toLocaleTimeString()
  }

  const getRateChange = (currency: string) => {
    // Simulate rate change for demo (in production, you'd track historical data)
    const change = (Math.random() - 0.5) * 0.002
    return change
  }

  const getRateChangeColor = (change: number) => {
    if (change > 0) return "text-green-600"
    if (change < 0) return "text-red-600"
    return "text-muted-foreground"
  }

  const getRateChangeIcon = (change: number) => {
    if (change > 0) return "↗"
    if (change < 0) return "↘"
    return "→"
  }

  if (compact) {
    return (
      <Card className="border-primary/20">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Live Rates</CardTitle>
            {showRefresh && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="border-primary/20 bg-transparent"
              >
                {isRefreshing ? "↻" : "⟳"}
              </Button>
            )}
          </div>
          <CardDescription>Last updated: {formatTime(lastUpdate)}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {Object.values(rates).map((rate) => {
              const change = getRateChange(rate.currency)
              return (
                <div
                  key={rate.currency}
                  className="flex items-center justify-between p-2 border border-primary/10 rounded"
                >
                  <span className="font-medium text-sm">{rate.currency}</span>
                  <div className="text-right">
                    <div className="font-bold text-sm">${rate.rate.toFixed(4)}</div>
                    <div className={`text-xs ${getRateChangeColor(change)}`}>
                      {getRateChangeIcon(change)} {Math.abs(change * 100).toFixed(2)}%
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-primary/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Exchange Rates</CardTitle>
            <CardDescription>Live stablecoin exchange rates</CardDescription>
          </div>
          {showRefresh && (
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="border-primary/20 bg-transparent"
            >
              {isRefreshing ? "Refreshing..." : "Refresh Rates"}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {exchangeRateService.isStale() && (
          <Alert className="border-yellow-500/20 bg-yellow-500/5">
            <AlertDescription className="text-yellow-700">
              Exchange rates may be outdated. Click refresh to get the latest rates.
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-3">
          {Object.values(rates).map((rate) => {
            const change = getRateChange(rate.currency)
            return (
              <div
                key={rate.currency}
                className="flex items-center justify-between p-4 border border-primary/10 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-primary">{rate.currency.slice(0, 2)}</span>
                  </div>
                  <div>
                    <p className="font-medium">{rate.currency}</p>
                    <p className="text-sm text-muted-foreground">Spread: {(rate.spread * 100).toFixed(2)}%</p>
                  </div>
                </div>

                <div className="text-right">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-primary">${rate.rate.toFixed(4)}</span>
                    <div className={`flex items-center gap-1 ${getRateChangeColor(change)}`}>
                      <span className="text-lg">{getRateChangeIcon(change)}</span>
                      <span className="text-sm font-medium">{Math.abs(change * 100).toFixed(2)}%</span>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      Buy: ${rate.buyRate.toFixed(4)}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      Sell: ${rate.sellRate.toFixed(4)}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">Updated: {rate.lastUpdated.toLocaleTimeString()}</p>
                </div>
              </div>
            )
          })}
        </div>

        <div className="text-center text-sm text-muted-foreground">Rates update automatically every 5 minutes</div>
      </CardContent>
    </Card>
  )
}

export default ExchangeRateWidget