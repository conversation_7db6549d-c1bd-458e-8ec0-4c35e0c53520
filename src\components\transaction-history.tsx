'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { TrendingUp, TrendingDown, Search, Filter } from 'lucide-react'
import { getTransactions } from '@/lib/auth'
import { getExchangeRates } from '@/lib/exchange-rates'

export function TransactionHistory() {
    const [transactions, setTransactions] = useState<any[]>([])
    const [filteredTransactions, setFilteredTransactions] = useState<any[]>([])
    const [loading, setLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState('')
    const [typeFilter, setTypeFilter] = useState('all')
    const [currentRate, setCurrentRate] = useState(0)

    useEffect(() => {
        const loadData = async () => {
            try {
                const [txData, rateData] = await Promise.all([
                    getTransactions(),
                    getExchangeRates()
                ])
                
                setTransactions(txData)
                setFilteredTransactions(txData)
                setCurrentRate(rateData.usd_mwk)
            } catch (error) {
                console.error('Failed to load transactions:', error)
            } finally {
                setLoading(false)
            }
        }

        loadData()
    }, [])

    useEffect(() => {
        let filtered = transactions

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(tx => 
                tx.counterparty.toLowerCase().includes(searchTerm.toLowerCase()) ||
                tx.txid?.toLowerCase().includes(searchTerm.toLowerCase())
            )
        }

        // Filter by type
        if (typeFilter !== 'all') {
            filtered = filtered.filter(tx => tx.type === typeFilter)
        }

        setFilteredTransactions(filtered)
    }, [transactions, searchTerm, typeFilter])

    const getTransactionIcon = (type: string) => {
        return type === 'buy' ? (
            <TrendingUp className="w-4 h-4 text-green-500" />
        ) : (
            <TrendingDown className="w-4 h-4 text-red-500" />
        )
    }

    const getTransactionBadge = (type: string) => {
        return type === 'buy' ? (
            <Badge variant="default" className="bg-green-100 text-green-800">Buy</Badge>
        ) : (
            <Badge variant="secondary" className="bg-red-100 text-red-800">Sell</Badge>
        )
    }

    const calculateProfit = (tx: any) => {
        const expectedMwk = tx.usdt_amount * currentRate
        return tx.mwk_amount - expectedMwk
    }

    if (loading) {
        return (
            <Card>
                <CardContent className="flex items-center justify-center py-8">
                    <div>Loading transactions...</div>
                </CardContent>
            </Card>
        )
    }

    return (
        <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{transactions.length}</div>
                        <p className="text-xs text-muted-foreground">All time</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Volume</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {transactions.reduce((sum, tx) => sum + tx.usdt_amount, 0).toFixed(2)} USDT
                        </div>
                        <p className="text-xs text-muted-foreground">Total traded</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            MWK {transactions.reduce((sum, tx) => sum + calculateProfit(tx), 0).toFixed(2)}
                        </div>
                        <p className="text-xs text-muted-foreground">Estimated profit</p>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle>Transaction History</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex gap-4 mb-6">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <Input
                                    placeholder="Search by counterparty or transaction ID..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <Select value={typeFilter} onValueChange={setTypeFilter}>
                            <SelectTrigger className="w-32">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Types</SelectItem>
                                <SelectItem value="buy">Buy</SelectItem>
                                <SelectItem value="sell">Sell</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Transaction List */}
                    {filteredTransactions.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                            {searchTerm || typeFilter !== 'all' ? 'No transactions match your filters' : 'No transactions found'}
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {filteredTransactions.map((transaction) => (
                                <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                    <div className="flex items-center gap-4">
                                        {getTransactionIcon(transaction.type)}
                                        <div>
                                            <div className="flex items-center gap-2">
                                                <p className="font-medium">{transaction.counterparty}</p>
                                                {getTransactionBadge(transaction.type)}
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(transaction.timestamp).toLocaleString()}
                                            </p>
                                            {transaction.txid && (
                                                <p className="text-xs text-muted-foreground">
                                                    TX: {transaction.txid.slice(0, 8)}...{transaction.txid.slice(-6)}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="font-bold">
                                            {transaction.usdt_amount.toFixed(2)} USDT
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            MWK {transaction.mwk_amount.toFixed(2)}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            Rate: {transaction.rate_used.toFixed(2)}
                                        </p>
                                        <p className={`text-xs ${calculateProfit(transaction) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                            Profit: MWK {calculateProfit(transaction).toFixed(2)}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}