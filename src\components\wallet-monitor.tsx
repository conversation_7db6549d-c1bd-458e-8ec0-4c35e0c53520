'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react'
import { getTronWalletInfo, validateTronAddress, type TronWalletInfo } from '@/lib/web3'
import { getCurrentUser } from '@/lib/auth'

export function WalletMonitor() {
    const [walletAddress, setWalletAddress] = useState('')
    const [walletInfo, setWalletInfo] = useState<TronWalletInfo | null>(null)
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState('')
    const [user, setUser] = useState<any>(null)

    useEffect(() => {
        const loadUser = async () => {
            const currentUser = await getCurrentUser()
            setUser(currentUser)
            if (currentUser?.wallet_address) {
                setWalletAddress(currentUser.wallet_address)
            }
        }
        loadUser()
    }, [])

    const handleMonitor = async () => {
        if (!walletAddress.trim()) {
            setError('Please enter a wallet address')
            return
        }

        if (!validateTronAddress(walletAddress)) {
            setError('Invalid TRON wallet address format')
            return
        }

        setLoading(true)
        setError('')

        try {
            const info = await getTronWalletInfo(walletAddress)
            setWalletInfo(info)
        } catch (err) {
            setError('Failed to fetch wallet information. Please try again.')
            console.error('Wallet monitoring error:', err)
        } finally {
            setLoading(false)
        }
    }

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Wallet Monitor</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="flex gap-2">
                            <Input
                                placeholder="Enter TRON wallet address (T...)"
                                value={walletAddress}
                                onChange={(e) => setWalletAddress(e.target.value)}
                                className="flex-1"
                            />
                            <Button onClick={handleMonitor} disabled={loading}>
                                {loading ? <RefreshCw className="w-4 h-4 animate-spin" /> : "Monitor"}
                            </Button>
                        </div>
                    </div>

                    {error && (
                        <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}
                </CardContent>
            </Card>

            {walletInfo && (
                <>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">USDT Balance</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold font-mono">
                                    {walletInfo.balance.toFixed(2)} USDT
                                </div>
                                <p className="text-xs text-muted-foreground">TRC20 Token</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Recent Transactions</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {walletInfo.transactions.length}
                                </div>
                                <p className="text-xs text-muted-foreground">Last 24 hours</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Wallet Status</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Badge variant="default" className="text-sm">
                                    Active
                                </Badge>
                                <p className="text-xs text-muted-foreground mt-1">Monitoring enabled</p>
                            </CardContent>
                        </Card>
                    </div>

                    {walletInfo.transactions.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Transactions</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {walletInfo.transactions.slice(0, 5).map((tx, index) => (
                                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div className="flex items-center gap-3">
                                                {tx.type === 'in' ? (
                                                    <TrendingUp className="w-4 h-4 text-green-500" />
                                                ) : (
                                                    <TrendingDown className="w-4 h-4 text-red-500" />
                                                )}
                                                <div>
                                                    <p className="font-medium">
                                                        {tx.type === 'in' ? '+' : '-'}{tx.amount.toFixed(2)} USDT
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(tx.timestamp).toLocaleString()}
                                                    </p>
                                                </div>
                                            </div>
                                            <Badge variant={tx.status === 'confirmed' ? 'default' : 'secondary'}>
                                                {tx.status}
                                            </Badge>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </>
            )}
        </div>
    )
}
