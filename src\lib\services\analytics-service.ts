import { db, transactions, profits, users } from '@/lib/db'
import { eq, and, desc, sum, count, gte, lte, sql } from 'drizzle-orm'
import { getExchangeRates } from '@/lib/exchange-rates'

export interface AdvancedAnalytics {
  volumeMetrics: VolumeMetrics
  profitMetrics: ProfitMetrics
  riskMetrics: RiskMetrics
  performanceMetrics: PerformanceMetrics
  forecasting: ForecastData[]
}

export interface VolumeMetrics {
  totalVolume: number
  dailyAverage: number
  weeklyGrowth: number
  monthlyGrowth: number
  transactionCount: number
  averageTransactionSize: number
}

export interface ProfitMetrics {
  grossProfit: number
  netProfit: number
  profitMargin: number
  supplierShare: number
  operatorShare: number
  fees: number
}

export interface RiskMetrics {
  volatilityScore: number
  liquidityRatio: number
  concentrationRisk: number
  operationalRisk: number
  overallRiskScore: number
}

export interface PerformanceMetrics {
  profitMarginGrade: string
  volumeGrowthGrade: string
  efficiencyScore: number
  overallScore: number
}

export interface ForecastData {
  period: string
  projectedVolume: number
  projectedProfit: number
  confidence: number
}

export class AnalyticsService {
  static async getAdvancedAnalytics(userId: string, period: string = '30d'): Promise<AdvancedAnalytics> {
    const dateRange = this.getDateRange(period)
    
    const userTransactions = await db.select()
      .from(transactions)
      .where(and(
        eq(transactions.userId, userId),
        gte(transactions.timestamp, dateRange.start),
        lte(transactions.timestamp, dateRange.end)
      ))
      .orderBy(desc(transactions.timestamp))

    const volumeMetrics = await this.calculateVolumeMetrics(userTransactions, period)
    const profitMetrics = await this.calculateProfitMetrics(userTransactions)
    const riskMetrics = await this.calculateRiskMetrics(userTransactions, userId)
    const performanceMetrics = this.calculatePerformanceMetrics(volumeMetrics, profitMetrics)
    const forecasting = await this.generateForecast(userTransactions)

    return {
      volumeMetrics,
      profitMetrics,
      riskMetrics,
      performanceMetrics,
      forecasting
    }
  }

  private static getDateRange(period: string) {
    const end = new Date()
    const start = new Date()
    
    switch (period) {
      case '7d':
        start.setDate(end.getDate() - 7)
        break
      case '30d':
        start.setDate(end.getDate() - 30)
        break
      case '90d':
        start.setDate(end.getDate() - 90)
        break
      default:
        start.setDate(end.getDate() - 30)
    }
    
    return { start, end }
  }

  private static async calculateVolumeMetrics(txs: any[], period: string): Promise<VolumeMetrics> {
    const totalVolume = txs.reduce((sum, tx) => sum + parseFloat(tx.usdtAmount), 0)
    const transactionCount = txs.length
    const averageTransactionSize = transactionCount > 0 ? totalVolume / transactionCount : 0
    
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
    const dailyAverage = totalVolume / days

    // Calculate growth rates (simplified - in production would compare to previous periods)
    const weeklyGrowth = Math.random() * 20 - 10 // Mock data
    const monthlyGrowth = Math.random() * 30 - 15 // Mock data

    return {
      totalVolume,
      dailyAverage,
      weeklyGrowth,
      monthlyGrowth,
      transactionCount,
      averageTransactionSize
    }
  }

  private static async calculateProfitMetrics(txs: any[]): Promise<ProfitMetrics> {
    const rates = await getExchangeRates()
    
    const totalUSDT = txs.reduce((sum, tx) => sum + parseFloat(tx.usdtAmount), 0)
    const totalMWK = txs.reduce((sum, tx) => sum + parseFloat(tx.mwkAmount), 0)
    
    const expectedMWK = totalUSDT * rates.usd_mwk
    const grossProfit = totalMWK - expectedMWK
    const fees = totalMWK * 0.001 // 0.1% fee
    const netProfit = grossProfit - fees
    
    const profitMargin = totalMWK > 0 ? (netProfit / totalMWK) * 100 : 0
    const supplierShare = netProfit * 0.6
    const operatorShare = netProfit * 0.4

    return {
      grossProfit,
      netProfit,
      profitMargin,
      supplierShare,
      operatorShare,
      fees
    }
  }

  private static async calculateRiskMetrics(txs: any[], userId: string): Promise<RiskMetrics> {
    // Volatility calculation
    const amounts = txs.map(tx => parseFloat(tx.usdtAmount))
    const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length
    const variance = amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length
    const volatilityScore = Math.min(100, (Math.sqrt(variance) / avgAmount) * 100)

    // Liquidity ratio (mock current balance)
    const currentBalance = 2450 // Would fetch from wallet
    const dailyVolume = txs
      .filter(tx => new Date(tx.timestamp) >= new Date(Date.now() - 24 * 60 * 60 * 1000))
      .reduce((sum, tx) => sum + parseFloat(tx.usdtAmount), 0)
    const liquidityRatio = dailyVolume > 0 ? (currentBalance / dailyVolume) * 100 : 100

    // Concentration risk
    const customerVolumes: Record<string, number> = {}
    txs.forEach(tx => {
      customerVolumes[tx.counterparty] = (customerVolumes[tx.counterparty] || 0) + parseFloat(tx.usdtAmount)
    })
    
    const totalVolume = Object.values(customerVolumes).reduce((sum, vol) => sum + vol, 0)
    const largestCustomer = Math.max(...Object.values(customerVolumes))
    const concentrationRisk = totalVolume > 0 ? (largestCustomer / totalVolume) * 100 : 0

    // Operational risk
    const recentTxCount = txs.filter(
      tx => new Date(tx.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length
    const operationalRisk = Math.max(0, 100 - recentTxCount * 2)

    const overallRiskScore = 
      volatilityScore * 0.3 + 
      (100 - liquidityRatio) * 0.3 + 
      concentrationRisk * 0.2 + 
      operationalRisk * 0.2

    return {
      volatilityScore,
      liquidityRatio,
      concentrationRisk,
      operationalRisk,
      overallRiskScore
    }
  }

  private static calculatePerformanceMetrics(volume: VolumeMetrics, profit: ProfitMetrics): PerformanceMetrics {
    const profitMarginGrade = profit.profitMargin >= 5 ? 'A' : 
                             profit.profitMargin >= 3 ? 'B' : 
                             profit.profitMargin >= 1 ? 'C' : 'D'

    const volumeGrowthGrade = volume.monthlyGrowth >= 15 ? 'A' :
                             volume.monthlyGrowth >= 5 ? 'B' :
                             volume.monthlyGrowth >= 0 ? 'C' : 'D'

    const efficiencyScore = Math.min(100, volume.averageTransactionSize / 100 + volume.transactionCount / 10)
    const overallScore = (profit.profitMargin * 2 + Math.max(0, volume.monthlyGrowth) + volume.transactionCount / 10) / 3

    return {
      profitMarginGrade,
      volumeGrowthGrade,
      efficiencyScore,
      overallScore
    }
  }

  private static async generateForecast(txs: any[]): Promise<ForecastData[]> {
    const forecast: ForecastData[] = []
    
    // Simple linear regression for forecasting
    const recentVolumes = txs.slice(-6).map(tx => parseFloat(tx.usdtAmount))
    const avgGrowth = recentVolumes.length > 1 ? 
      (recentVolumes[recentVolumes.length - 1] - recentVolumes[0]) / recentVolumes.length : 0

    for (let i = 1; i <= 6; i++) {
      const currentVolume = recentVolumes[recentVolumes.length - 1] || 0
      const projectedVolume = Math.max(0, currentVolume + (avgGrowth * i))
      const projectedProfit = projectedVolume * 0.025 // 2.5% profit margin
      const confidence = Math.max(20, 90 - (i * 10)) // Decreasing confidence

      forecast.push({
        period: `Month ${i}`,
        projectedVolume,
        projectedProfit,
        confidence
      })
    }

    return forecast
  }
}