"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"
import { getCurrentUser, logout, getExchangeRates, getUSDTBalance, type User } from "@/lib/auth"
import { Wallet, Settings, LogOut, Bell, Menu, X } from "lucide-react"

interface DashboardLayoutProps {
    children: React.ReactNode
    activeTab: string
    onTabChange: (tab: string) => void
    tabs: Array<{
        id: string
        label: string
        icon: React.ReactNode
    }>
}

interface ExchangeRates {
    usd_mwk: number
    black_market_rate: number
    usd_zar: number | string
}

interface USDTBalance {
    balance: number
    trx_balance: number | string
}

// Default values for fallback
const DEFAULT_RATES: ExchangeRates = {
    usd_mwk: 0,
    black_market_rate: 0,
    usd_zar: "0.00"
}

const DEFAULT_BALANCE: USDTBalance = {
    balance: 0,
    trx_balance: "0"
}

export function DashboardLayout({ children, activeTab, onTabChange, tabs }: DashboardLayoutProps) {
    const [user, setUser] = useState<User | null>(null)
    const [sidebarOpen, setSidebarOpen] = useState(false)
    const [rates, setRates] = useState<ExchangeRates>(DEFAULT_RATES)
    const [usdtBalance, setUsdtBalance] = useState<USDTBalance>(DEFAULT_BALANCE)
    const [isLoading, setIsLoading] = useState(true)
    const router = useRouter()

    useEffect(() => {
        const initializeData = async () => {
            try {
                setIsLoading(true)
                const currentUser = getCurrentUser()

                if (!currentUser) {
                    router.push("/")
                    return
                }

                setUser(currentUser)

                // Safely get exchange rates with error handling
                try {
                    const ratesData = getExchangeRates()
                    setRates(ratesData || DEFAULT_RATES)
                } catch (error) {
                    console.error("Error loading exchange rates:", error)
                    setRates(DEFAULT_RATES)
                }

                // Safely get USDT balance with error handling
                try {
                    const balanceData = getUSDTBalance()
                    setUsdtBalance(balanceData || DEFAULT_BALANCE)
                } catch (error) {
                    console.error("Error loading USDT balance:", error)
                    setUsdtBalance(DEFAULT_BALANCE)
                }
            } catch (error) {
                console.error("Error initializing dashboard:", error)
                router.push("/")
            } finally {
                setIsLoading(false)
            }
        }

        initializeData()
    }, [router])

    const handleLogout = () => {
        logout()
        router.push("/")
    }

    // Safe number formatting with fallbacks
    const formatNumber = (value: number | string | undefined, fallback: string = "0"): string => {
        if (value === undefined || value === null) return fallback

        const numValue = typeof value === 'string' ? parseFloat(value) : value
        return isNaN(numValue) ? fallback : numValue.toLocaleString()
    }

    // Safe number formatting for fixed decimals
    const formatFixed = (value: number | string | undefined, decimals: number = 2): string => {
        if (value === undefined || value === null) return "0.00"

        const numValue = typeof value === 'string' ? parseFloat(value) : value
        return isNaN(numValue) ? "0.00" : numValue.toFixed(decimals)
    }

    if (isLoading) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-4 text-muted-foreground">Loading dashboard...</p>
                </div>
            </div>
        )
    }

    if (!user) {
        return (
            <div className="min-h-screen bg-background flex items-center justify-center">
                <div className="text-center">
                    <p className="text-destructive">Authentication failed</p>
                    <Button onClick={() => router.push("/")} className="mt-4">
                        Return to Login
                    </Button>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-background flex">
            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black/50 z-40 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}

            {/* Sidebar */}
            <div
                className={`
                    fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out
                    flex flex-col
                    ${sidebarOpen ? "translate-x-0" : "-translate-x-full"}
                    lg:translate-x-0 lg:static lg:inset-0
                `}
            >
                {/* Header */}
                <div className="flex-shrink-0 flex items-center justify-between p-4 border-b border-border">
                    <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                            <Wallet className="w-5 h-5 text-primary-foreground" />
                        </div>
                        <h1 className="text-lg font-bold">TEVAVEND</h1>
                    </div>
                    <Button
                        variant="ghost"
                        size="icon"
                        className="lg:hidden"
                        onClick={() => setSidebarOpen(false)}
                    >
                        <X className="w-4 h-4" />
                    </Button>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 flex flex-col overflow-hidden">
                    {/* User Info */}
                    <div className="flex-shrink-0 p-4 border-b border-border">
                        <div className="flex items-center space-x-3">
                            <Avatar>
                                <AvatarFallback className="bg-primary text-primary-foreground">
                                    {user?.name ? user.name.charAt(0).toUpperCase() : "U"}
                                </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{user?.name || "Unknown User"}</p>
                                <div className="flex items-center space-x-2">
                                    <Badge variant={user?.role === "supplier" ? "default" : "secondary"}>
                                        {user?.role || "user"}
                                    </Badge>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Exchange Rates */}
                    <div className="flex-shrink-0 p-4 border-b border-border">
                        <h3 className="text-xs font-medium text-muted-foreground mb-2">LIVE RATES</h3>
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span>USD/MWK</span>
                                <span className="font-mono">{formatNumber(rates.usd_mwk)}</span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span>Black Market</span>
                                <span className="font-mono text-chart-1">
                                    {formatNumber(rates.black_market_rate)}
                                </span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span>USD/ZAR</span>
                                <span className="font-mono">{formatNumber(rates.usd_zar)}</span>
                            </div>
                        </div>
                    </div>

                    {/* USDT Balance */}
                    <div className="flex-shrink-0 p-4 border-b border-border">
                        <h3 className="text-xs font-medium text-muted-foreground mb-2">WALLET</h3>
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span>USDT</span>
                                <span className="font-mono font-medium">
                                    {formatFixed(usdtBalance.balance)}
                                </span>
                            </div>
                            <div className="flex justify-between text-sm">
                                <span>TRX</span>
                                <span className="font-mono text-muted-foreground">
                                    {formatNumber(usdtBalance.trx_balance)}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Navigation - Scrollable if needed */}
                    <div className="flex-1 overflow-y-auto">
                        <nav className="p-4">
                            <div className="space-y-1">
                                {tabs.map((tab) => (
                                    <Button
                                        key={tab.id}
                                        variant={activeTab === tab.id ? "secondary" : "ghost"}
                                        className="w-full justify-start"
                                        onClick={() => {
                                            onTabChange(tab.id)
                                            setSidebarOpen(false)
                                        }}
                                    >
                                        {tab.icon}
                                        {tab.label}
                                    </Button>
                                ))}
                            </div>
                        </nav>
                    </div>

                    {/* Footer */}
                    <div className="flex-shrink-0 p-4 border-t border-border">
                        <div className="space-y-2">
                            <Button variant="ghost" className="w-full justify-start" size="sm">
                                <Settings className="w-4 h-4 mr-2" />
                                Settings
                            </Button>
                            <Button
                                variant="ghost"
                                className="w-full justify-start text-destructive hover:text-destructive"
                                size="sm"
                                onClick={handleLogout}
                            >
                                <LogOut className="w-4 h-4 mr-2" />
                                Sign Out
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 flex flex-col min-w-0">
                {/* Top Bar */}
                <header className="flex-shrink-0 bg-card border-b border-border px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Button
                                variant="ghost"
                                size="icon"
                                className="lg:hidden"
                                onClick={() => setSidebarOpen(true)}
                            >
                                <Menu className="w-4 h-4" />
                            </Button>
                            <div>
                                <h2 className="text-lg font-semibold capitalize">
                                    {tabs.find((tab) => tab.id === activeTab)?.label || "Dashboard"}
                                </h2>
                                <p className="text-sm text-muted-foreground">
                                    {user?.role === "supplier" ? "Liquidity Management" : "Vending Operations"}
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="icon">
                                <Bell className="w-4 h-4" />
                            </Button>
                            <ThemeToggle />
                        </div>
                    </div>
                </header>

                {/* Page Content - Scrollable */}
                <main className="flex-1 overflow-auto p-4">
                    {children}
                </main>
            </div>
        </div>
    )
}