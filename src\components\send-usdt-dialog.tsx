"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { validateTronAddress, estimateGasFee, sendUSDT } from "@/lib/web3"
import { Send, AlertTriangle, CheckCircle, Loader2 } from "lucide-react"

interface SendUSDTDialogProps {
  recipientAddress: string
  recipientLabel: string
  fromAddress: string
  currentBalance: number
  trxBalance: number
}

export function SendUSDTDialog({
  recipientAddress,
  recipientLabel,
  fromAddress,
  currentBalance,
  trxBalance,
}: SendUSDTDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [formData, setFormData] = useState({
    toAddress: "",
    amount: "",
    notes: "",
  })
  const [sending, setSending] = useState(false)
  const [txResult, setTxResult] = useState<{ txid: string; status: string } | null>(null)
  const [error, setError] = useState("")

  const amount = Number.parseFloat(formData.amount) || 0
  const [estimatedFee, setEstimatedFee] = useState(0)

  // Update estimatedFee whenever amount changes
  useEffect(() => {
    let isMounted = true
    estimateGasFee(amount).then(fee => {
      if (isMounted) setEstimatedFee(fee)
    })
    return () => { isMounted = false }
  }, [amount])

  const hasValidAddress = validateTronAddress(formData.toAddress)
  const hasValidAmount = amount > 0 && amount <= currentBalance
  const hasSufficientGas = trxBalance >= estimatedFee
  const canSend = hasValidAddress && hasValidAmount && hasSufficientGas && !sending

  const handleSend = async () => {
    if (!canSend) return

    setSending(true)
    setError("")
    setTxResult(null)

    try {
      const result = await sendUSDT(fromAddress, formData.toAddress, amount)
      setTxResult(result)

      // Reset form on success
      if (result.status !== "failed") {
        setFormData({ toAddress: "", amount: "", notes: "" })
      }
    } catch (err) {
      setError("Failed to send transaction. Please try again.")
      console.error(err)
    } finally {
      setSending(false)
    }
  }

  const resetDialog = () => {
    setFormData({ toAddress: "", amount: "", notes: "" })
    setError("")
    setTxResult(null)
    setSending(false)
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open)
        if (!open) resetDialog()
      }}
    >
      <DialogTrigger asChild>
        <Button>
          <Send className="w-4 h-4 mr-2" />
          Send USDT
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Send USDT (TRC20)</DialogTitle>
          <DialogDescription>Send USDT tokens to another TRON address</DialogDescription>
        </DialogHeader>

        {txResult ? (
          <div className="space-y-4">
            <div className="text-center py-6">
              <CheckCircle className="w-12 h-12 text-chart-1 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Transaction Submitted</h3>
              <p className="text-sm text-muted-foreground mb-4">Your USDT transfer has been submitted to the network</p>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Amount:</span>
                  <span className="font-mono">{amount} USDT</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>To:</span>
                  <span className="font-mono text-xs">
                    {formData.toAddress.slice(0, 8)}...{formData.toAddress.slice(-6)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>TXID:</span>
                  <span className="font-mono text-xs">
                    {txResult.txid.slice(0, 8)}...{txResult.txid.slice(-6)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Status:</span>
                  <Badge variant="secondary">{txResult.status}</Badge>
                </div>
              </div>
            </div>
            <Button onClick={() => setIsOpen(false)} className="w-full">
              Close
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="toAddress">Recipient Address</Label>
              <Input
                id="toAddress"
                placeholder="TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE"
                value={formData.toAddress}
                onChange={(e) => setFormData({ ...formData, toAddress: e.target.value })}
                className="font-mono text-sm"
              />
              {formData.toAddress && !hasValidAddress && (
                <p className="text-xs text-destructive">Invalid TRON address</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount (USDT)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="100.00"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="font-mono"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Available: {currentBalance.toFixed(2)} USDT</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 px-1 text-xs"
                  onClick={() => setFormData({ ...formData, amount: currentBalance.toString() })}
                >
                  Max
                </Button>
              </div>
              {amount > currentBalance && <p className="text-xs text-destructive">Insufficient balance</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Transaction notes..."
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={2}
              />
            </div>

            {/* Transaction Summary */}
            {amount > 0 && (
              <div className="bg-muted/50 rounded-lg p-3 space-y-2">
                <h4 className="text-sm font-medium">Transaction Summary</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-mono">{amount.toFixed(2)} USDT</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Estimated Fee:</span>
                    <span className="font-mono">{estimatedFee.toFixed(1)} TRX</span>
                  </div>
                  <div className="flex justify-between">
                    <span>TRX Balance:</span>
                    <span className={`font-mono ${hasSufficientGas ? "text-chart-1" : "text-destructive"}`}>
                      {trxBalance.toFixed(1)} TRX
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Warnings */}
            {!hasSufficientGas && amount > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Insufficient TRX for transaction fees. Need at least {estimatedFee.toFixed(1)} TRX.
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsOpen(false)} className="flex-1">
                Cancel
              </Button>
              <Button onClick={handleSend} disabled={!canSend} className="flex-1">
                {sending ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <Send className="w-4 h-4 mr-2" />}
                Send USDT
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
