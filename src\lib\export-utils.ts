export interface ExportData {
    transactions?: any[]
    profits?: any[]
    settlements?: any[]
    dateRange?: { start: string; end: string }
    userRole?: string
}

export function exportToCSV(data: any[], filename: string) {
    if (data.length === 0) return

    const headers = Object.keys(data[0])
    const csvContent = [
        headers.join(","),
        ...data.map((row) =>
            headers
                .map((header) => {
                    const value = row[header]
                    // Escape commas and quotes in CSV
                    if (typeof value === "string" && (value.includes(",") || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`
                    }
                    return value
                })
                .join(","),
        ),
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `${filename}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

export function exportToPDF(data: ExportData, filename: string) {
    // Mock PDF export - in real implementation, use libraries like jsPDF
    const content = JSON.stringify(data, null, 2)
    const blob = new Blob([content], { type: "application/json" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `${filename}.json`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}

export function generateSettlementReport(transactions: any[], dateRange: { start: string; end: string }) {
    const filteredTransactions = transactions.filter((t) => {
        const transactionDate = new Date(t.timestamp)
        const startDate = new Date(dateRange.start)
        const endDate = new Date(dateRange.end)
        return transactionDate >= startDate && transactionDate <= endDate
    })

    const totalVolume = filteredTransactions.reduce((sum, t) => sum + t.usdt_amount, 0)
    const totalProfit = filteredTransactions.reduce((sum, t) => sum + t.mwk_amount * 0.025, 0)
    const transactionCount = filteredTransactions.length

    return {
        dateRange,
        totalVolume,
        totalProfit,
        transactionCount,
        transactions: filteredTransactions,
        generatedAt: new Date().toISOString(),
    }
}
