"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import transactionManager, { type Currency, type TransactionType } from "@/lib/transaction-manager"
import FinancialCalculationsEngine from "@/lib/financial-caluclations2"
import { useAuth } from "@/lib/auth"

export function TransactionForm() {
  const { user } = useAuth()
  const [formData, setFormData] = useState({
    amount: "",
    currency: "USDC" as Currency,
    type: "buy" as TransactionType,
    location: "",
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)
  const [feePreview, setFeePreview] = useState<any>(null)

  const handleAmountChange = (amount: string) => {
    setFormData((prev) => ({ ...prev, amount }))

    // Calculate fee preview
    const numAmount = Number.parseFloat(amount)
    if (!isNaN(numAmount) && numAmount > 0) {
      const fees = FinancialCalculationsEngine.calculateTransactionFees(numAmount, formData.currency)
      setFeePreview(fees)
    } else {
      setFeePreview(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setIsProcessing(true)
    setResult(null)

    const numAmount = Number.parseFloat(formData.amount)

    try {
      const response = await transactionManager.createTransaction(
        user.id,
        user.name,
        "1", // Mock supplier ID
        numAmount,
        formData.currency,
        formData.type,
        1.0, // Mock exchange rate
        {
          location: formData.location || undefined,
        },
      )

      if (response.success) {
        setResult({
          success: true,
          message: `Transaction ${response.transaction?.id} created successfully!`,
        })
        setFormData({
          amount: "",
          currency: "USDC",
          type: "buy",
          location: "",
        })
        setFeePreview(null)
      } else {
        setResult({
          success: false,
          message: response.error || "Transaction failed",
        })
      }
    } catch (error) {
      setResult({
        success: false,
        message: "An unexpected error occurred",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  if (user?.role !== "operator") {
    return (
      <Card className="border-primary/20">
        <CardContent className="pt-6">
          <Alert>
            <AlertDescription>Transaction processing is only available for operators.</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-primary/20">
      <CardHeader>
        <CardTitle>Process Transaction</CardTitle>
        <CardDescription>Create a new stablecoin transaction</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Amount (USD)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="10"
                max="10000"
                placeholder="Enter amount"
                value={formData.amount}
                onChange={(e) => handleAmountChange(e.target.value)}
                required
                className="border-primary/20 focus:border-primary"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select
                value={formData.currency}
                onValueChange={(value: Currency) => {
                  setFormData((prev) => ({ ...prev, currency: value }))
                  // Recalculate fees with new currency
                  if (formData.amount) {
                    handleAmountChange(formData.amount)
                  }
                }}
              >
                <SelectTrigger className="border-primary/20 focus:border-primary">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USDC">USDC</SelectItem>
                  <SelectItem value="USDT">USDT</SelectItem>
                  <SelectItem value="BUSD">BUSD</SelectItem>
                  <SelectItem value="DAI">DAI</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Transaction Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value: TransactionType) => setFormData((prev) => ({ ...prev, type: value }))}
              >
                <SelectTrigger className="border-primary/20 focus:border-primary">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="buy">Buy</SelectItem>
                  <SelectItem value="sell">Sell</SelectItem>
                  <SelectItem value="transfer">Transfer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location (Optional)</Label>
              <Input
                id="location"
                placeholder="e.g., Downtown Branch"
                value={formData.location}
                onChange={(e) => setFormData((prev) => ({ ...prev, location: e.target.value }))}
                className="border-primary/20 focus:border-primary"
              />
            </div>
          </div>

          {feePreview && (
            <div className="p-4 bg-muted/50 rounded-lg space-y-3">
              <h4 className="font-medium text-sm">Transaction Preview</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Amount</span>
                  <span className="font-medium">${formData.amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Your Commission</span>
                  <Badge variant="secondary" className="bg-primary/20 text-primary-foreground">
                    ${feePreview.operatorCommission}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Network Fee</span>
                  <span>${feePreview.networkFee}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Fees</span>
                  <span>${feePreview.totalFees}</span>
                </div>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Customer Receives</span>
                  <span className="font-bold text-primary">
                    ${(Number.parseFloat(formData.amount) - feePreview.totalFees).toFixed(2)} {formData.currency}
                  </span>
                </div>
              </div>
            </div>
          )}

          {result && (
            <Alert
              className={result.success ? "border-primary/20 bg-primary/5" : "border-destructive/20 bg-destructive/5"}
            >
              <AlertDescription className={result.success ? "text-primary" : "text-destructive"}>
                {result.message}
              </AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
            disabled={isProcessing || !formData.amount}
          >
            {isProcessing ? "Processing Transaction..." : "Process Transaction"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

export default TransactionForm