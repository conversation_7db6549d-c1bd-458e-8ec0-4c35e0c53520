'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Plus, Clock, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { SettlementService, type SettlementAnalytics } from '@/lib/services/settlement-service'
import { getCurrentUser } from '@/lib/auth'
import { validateTronAddress } from '@/lib/web3'

export function SettlementManager() {
    const [settlements, setSettlements] = useState<any[]>([])
    const [analytics, setAnalytics] = useState<SettlementAnalytics | null>(null)
    const [loading, setLoading] = useState(false)
    const [user, setUser] = useState<any>(null)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    
    // Form state
    const [amount, setAmount] = useState('')
    const [recipient, setRecipient] = useState('')
    const [notes, setNotes] = useState('')
    const [error, setError] = useState('')

    useEffect(() => {
        const loadData = async () => {
            const currentUser = await getCurrentUser()
            if (!currentUser) return
            
            setUser(currentUser)
            await loadSettlements(currentUser.id)
            await loadAnalytics(currentUser.id)
        }
        loadData()
    }, [])

    const loadSettlements = async (userId: string) => {
        try {
            const data = await SettlementService.getSettlements(userId)
            setSettlements(data)
        } catch (error) {
            console.error('Failed to load settlements:', error)
        }
    }

    const loadAnalytics = async (userId: string) => {
        try {
            const data = await SettlementService.getSettlementAnalytics(userId)
            setAnalytics(data)
        } catch (error) {
            console.error('Failed to load analytics:', error)
        }
    }

    const handleCreateSettlement = async () => {
        if (!user) return

        setError('')
        
        if (!amount || parseFloat(amount) <= 0) {
            setError('Please enter a valid amount')
            return
        }

        if (!recipient || !validateTronAddress(recipient)) {
            setError('Please enter a valid TRON wallet address')
            return
        }

        setLoading(true)

        try {
            await SettlementService.createSettlement({
                amount: parseFloat(amount),
                recipient,
                notes,
                userId: user.id
            })

            // Reset form
            setAmount('')
            setRecipient('')
            setNotes('')
            setIsDialogOpen(false)

            // Reload data
            await loadSettlements(user.id)
            await loadAnalytics(user.id)
        } catch (error: any) {
            setError(error.message || 'Failed to create settlement')
        } finally {
            setLoading(false)
        }
    }

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="w-4 h-4 text-green-500" />
            case 'processing':
                return <Clock className="w-4 h-4 text-yellow-500" />
            case 'failed':
                return <XCircle className="w-4 h-4 text-red-500" />
            default:
                return <Clock className="w-4 h-4 text-gray-500" />
        }
    }

    const getStatusVariant = (status: string) => {
        switch (status) {
            case 'completed':
                return 'default'
            case 'processing':
                return 'secondary'
            case 'failed':
                return 'destructive'
            default:
                return 'outline'
        }
    }

    return (
        <div className="space-y-6">
            {/* Analytics Cards */}
            {analytics && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total Settled</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {analytics.totalSettled.toFixed(2)} USDT
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Pending Amount</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {analytics.pendingAmount.toFixed(2)} USDT
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {analytics.successRate.toFixed(1)}%
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Avg Processing</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {analytics.averageProcessingTime.toFixed(1)}h
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Settlements List */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Settlement Requests</CardTitle>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="w-4 h-4 mr-2" />
                                New Settlement
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Create Settlement Request</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="amount">Amount (USDT)</Label>
                                    <Input
                                        id="amount"
                                        type="number"
                                        step="0.01"
                                        value={amount}
                                        onChange={(e) => setAmount(e.target.value)}
                                        placeholder="0.00"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="recipient">Recipient Address</Label>
                                    <Input
                                        id="recipient"
                                        value={recipient}
                                        onChange={(e) => setRecipient(e.target.value)}
                                        placeholder="TRON wallet address (T...)"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="notes">Notes (Optional)</Label>
                                    <Textarea
                                        id="notes"
                                        value={notes}
                                        onChange={(e) => setNotes(e.target.value)}
                                        placeholder="Additional notes..."
                                    />
                                </div>
                                {error && (
                                    <Alert variant="destructive">
                                        <AlertTriangle className="h-4 w-4" />
                                        <AlertDescription>{error}</AlertDescription>
                                    </Alert>
                                )}
                                <Button 
                                    onClick={handleCreateSettlement} 
                                    disabled={loading}
                                    className="w-full"
                                >
                                    {loading ? 'Creating...' : 'Create Settlement'}
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </CardHeader>
                <CardContent>
                    {settlements.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                            No settlement requests found
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {settlements.map((settlement) => (
                                <div key={settlement.id} className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-3">
                                        {getStatusIcon(settlement.status)}
                                        <div>
                                            <p className="font-medium">
                                                {parseFloat(settlement.amount).toFixed(2)} USDT
                                            </p>
                                            <p className="text-sm text-muted-foreground">
                                                To: {settlement.recipient.slice(0, 8)}...{settlement.recipient.slice(-6)}
                                            </p>
                                            <p className="text-xs text-muted-foreground">
                                                {new Date(settlement.createdAt).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <Badge variant={getStatusVariant(settlement.status)}>
                                            {settlement.status}
                                        </Badge>
                                        {settlement.txid && (
                                            <p className="text-xs text-muted-foreground mt-1">
                                                TX: {settlement.txid.slice(0, 8)}...
                                            </p>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
