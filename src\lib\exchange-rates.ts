// Real exchange rate API integration for USD/MWK/ZAR conversion
// Uses multiple APIs for reliability and accuracy

export interface ExchangeRates {
    usd_mwk: number
    usd_zar: number
    black_market_rate?: number
    last_updated: string
    source: string
}

// Primary exchange rate API (ExchangeRate-API)
const EXCHANGE_API_BASE = "https://api.exchangerate-api.com/v4/latest"

// Backup API (Fixer.io - requires API key)
const FIXER_API_BASE = "https://api.fixer.io/latest"

// Real-time exchange rate fetching
export const getExchangeRates = async (): Promise<ExchangeRates> => {
    try {
        // Try primary API first
        const response = await fetch(`${EXCHANGE_API_BASE}/USD`)
        const data = await response.json()

        if (data.rates) {
            return {
                usd_mwk: data.rates.MWK || 1650, // Fallback if MWK not available
                usd_zar: data.rates.ZAR || 18.5,
                black_market_rate: (data.rates.MWK || 1650) * 1.06, // Typically 6% higher
                last_updated: data.date || new Date().toISOString(),
                source: "ExchangeRate-API",
            }
        }

        throw new Error("Invalid response from primary API")
    } catch (error) {
        console.error("Primary exchange API failed:", error)

        try {
            // Try backup method - manual rate fetching from reliable sources
            const backupRates = await getBackupExchangeRates()
            return backupRates
        } catch (backupError) {
            console.error("Backup exchange API failed:", backupError)

            // Return cached rates or fallback
            return getFallbackRates()
        }
    }
}

// Backup exchange rate fetching
const getBackupExchangeRates = async (): Promise<ExchangeRates> => {
    // Try to get rates from a different source
    const response = await fetch("https://api.fxratesapi.com/latest?base=USD&symbols=MWK,ZAR")
    const data = await response.json()

    if (data.rates) {
        return {
            usd_mwk: data.rates.MWK || 1650,
            usd_zar: data.rates.ZAR || 18.5,
            black_market_rate: (data.rates.MWK || 1650) * 1.06,
            last_updated: data.date || new Date().toISOString(),
            source: "FXRatesAPI",
        }
    }

    throw new Error("Backup API also failed")
}

// Fallback rates (cached or default values)
const getFallbackRates = (): ExchangeRates => {
    // Try to get cached rates from localStorage
    if (typeof window !== "undefined") {
        const cached = localStorage.getItem("tevavend_cached_rates")
        if (cached) {
            const cachedRates = JSON.parse(cached)
            // Use cached rates if they're less than 1 hour old
            const cacheAge = Date.now() - new Date(cachedRates.last_updated).getTime()
            if (cacheAge < 60 * 60 * 1000) {
                // 1 hour
                return {
                    ...cachedRates,
                    source: "Cached",
                }
            }
        }
    }

    // Return default fallback rates
    return {
        usd_mwk: 1650,
        usd_zar: 18.5,
        black_market_rate: 1750,
        last_updated: new Date().toISOString(),
        source: "Fallback",
    }
}

// Cache exchange rates for offline use
export const cacheExchangeRates = (rates: ExchangeRates) => {
    if (typeof window !== "undefined") {
        localStorage.setItem("tevavend_cached_rates", JSON.stringify(rates))
    }
}

// Get historical exchange rates for reporting
export const getHistoricalRates = async (date: string, baseCurrency = "USD"): Promise<ExchangeRates | null> => {
    try {
        const response = await fetch(`https://api.exchangerate-api.com/v4/history/${baseCurrency}/${date}`)
        const data = await response.json()

        if (data.rates) {
            return {
                usd_mwk: data.rates.MWK || 1650,
                usd_zar: data.rates.ZAR || 18.5,
                black_market_rate: (data.rates.MWK || 1650) * 1.06,
                last_updated: data.date || date,
                source: "Historical",
            }
        }

        return null
    } catch (error) {
        console.error("Error fetching historical rates:", error)
        return null
    }
}

// Real-time rate monitoring with WebSocket (for premium features)
export const subscribeToRateUpdates = (callback: (rates: ExchangeRates) => void): (() => void) => {
    // Set up periodic polling since most free APIs don't have WebSocket
    const interval = setInterval(
        async () => {
            try {
                const rates = await getExchangeRates()
                cacheExchangeRates(rates)
                callback(rates)
            } catch (error) {
                console.error("Error in rate update subscription:", error)
            }
        },
        5 * 60 * 1000,
    ) // Update every 5 minutes

    // Return cleanup function
    return () => clearInterval(interval)
}

// Currency conversion with real-time rates
export const convertCurrency = async (
    amount: number,
    fromCurrency: "USD" | "MWK" | "ZAR",
    toCurrency: "USD" | "MWK" | "ZAR",
): Promise<number> => {
    if (fromCurrency === toCurrency) return amount

    const rates = await getExchangeRates()

    // Convert to USD first
    let usdAmount = amount
    if (fromCurrency === "MWK") {
        usdAmount = amount / rates.usd_mwk
    } else if (fromCurrency === "ZAR") {
        usdAmount = amount / rates.usd_zar
    }

    // Convert from USD to target currency
    if (toCurrency === "MWK") {
        return usdAmount * rates.usd_mwk
    } else if (toCurrency === "ZAR") {
        return usdAmount * rates.usd_zar
    }

    return usdAmount
}
