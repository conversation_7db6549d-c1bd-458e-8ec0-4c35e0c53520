"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/lib/auth"

export function LoginForm() {
    const [email, setEmail] = useState("")
    const [password, setPassword] = useState("")
    const [error, setError] = useState("")
    const { login, isLoading } = useAuth()
    const router = useRouter()

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setError("")

        const success = await login(email, password)
        if (success) {
            // The useEffect in the route pages will handle the actual redirect
            router.refresh()
        } else {
            setError("Invalid email or password")
        }
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-secondary/5 p-4">
            <Card className="w-full max-w-md border-primary/20 shadow-xl">
                <CardHeader className="text-center space-y-4">
                    <div className="mx-auto w-16 h-16 bg-primary rounded-xl flex items-center justify-center">
                        <div className="text-2xl font-bold text-primary-foreground">TV</div>
                    </div>
                    <div>
                        <CardTitle className="text-2xl font-bold text-balance">Welcome to TEVAVEND</CardTitle>
                        <CardDescription className="text-muted-foreground">
                            International Stablecoin Vending & Remittance System
                        </CardDescription>
                    </div>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="email">Email</Label>
                            <Input
                                id="email"
                                type="email"
                                placeholder="Enter your email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                required
                                className="border-primary/20 focus:border-primary"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="password">Password</Label>
                            <Input
                                id="password"
                                type="password"
                                placeholder="Enter your password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                required
                                className="border-primary/20 focus:border-primary"
                            />
                        </div>

                        {error && (
                            <Alert className="border-destructive/20 bg-destructive/5">
                                <AlertDescription className="text-destructive">{error}</AlertDescription>
                            </Alert>
                        )}

                        <Button
                            type="submit"
                            className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                            disabled={isLoading}
                        >
                            {isLoading ? "Signing in..." : "Sign In"}
                        </Button>
                    </form>

                    <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-2">Demo Credentials:</p>
                        <div className="text-xs space-y-1">
                            <div>
                                <strong>Supplier:</strong> <EMAIL> / supplier123
                            </div>
                            <div>
                                <strong>Operator:</strong> <EMAIL> / operator123
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
