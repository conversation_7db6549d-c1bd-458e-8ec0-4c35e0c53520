'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { AnalyticsDashboard } from './dashboard/analytics-dashboard'
import { WalletMonitor } from './wallet-monitor'
import { SettlementManager } from './settlement-manager'
import { TransactionHistory } from './transaction-history'
import { getCurrentUser, type User } from '@/lib/auth'
import { Card, CardContent } from '@/components/ui/card'

export function Dashboard() {
    const [user, setUser] = useState<User | null>(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        const loadUser = async () => {
            try {
                const currentUser = await getCurrentUser()
                setUser(currentUser)
            } catch (error) {
                console.error('Failed to load user:', error)
            } finally {
                setLoading(false)
            }
        }

        loadUser()
    }, [])

    if (loading) {
        return (
            <Card>
                <CardContent className="flex items-center justify-center py-8">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                        <p className="mt-2">Loading dashboard...</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    if (!user) {
        return (
            <Card>
                <CardContent className="flex items-center justify-center py-8">
                    <div className="text-center">
                        <p className="text-destructive">Please log in to access the dashboard</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-3xl font-bold">Dashboard</h1>
                <p className="text-muted-foreground">
                    Welcome back, {user.nick_name || user.email}
                </p>
            </div>

            <Tabs defaultValue="analytics" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                    <TabsTrigger value="transactions">Transactions</TabsTrigger>
                    <TabsTrigger value="wallet">Wallet</TabsTrigger>
                    <TabsTrigger value="settlements">Settlements</TabsTrigger>
                </TabsList>

                <TabsContent value="analytics">
                    <AnalyticsDashboard />
                </TabsContent>

                <TabsContent value="transactions">
                    <TransactionHistory />
                </TabsContent>

                <TabsContent value="wallet">
                    <WalletMonitor />
                </TabsContent>

                <TabsContent value="settlements">
                    <SettlementManager />
                </TabsContent>
            </Tabs>
        </div>
    )
}
