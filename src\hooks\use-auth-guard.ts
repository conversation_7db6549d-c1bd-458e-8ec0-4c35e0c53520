// hooks/useAuthGuard.ts
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/utils/supabaseClient'

export function useAuthGuard(requiredRole?: 'Operator' | 'Supplier') {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error || !session) {
          router.push('/login')
          return
        }

        // Get user role from users table
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()

        if (userError || !userData) {
          console.error('Error fetching user data:', userError)
          router.push('/login')
          return
        }

        setUser(userData)

        // Check role if required
        if (requiredRole && userData.role !== requiredRole) {
          // Redirect to appropriate dashboard based on actual role
          const redirectPath = userData.role === 'Supplier' ? '/supplier' : '/operator'
          router.push(redirectPath)
          return
        }

      } catch (error) {
        console.error('Auth guard error:', error)
        router.push('/login')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_OUT') {
        router.push('/login')
      } else if (session) {
        // Refresh user data on auth state change
        const { data: userData } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()
        
        if (userData) {
          setUser(userData)
        }
      }
    })

    return () => subscription.unsubscribe()
  }, [requiredRole, router])

  return { user, loading }
}