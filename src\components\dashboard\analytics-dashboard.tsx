'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { TrendingUp, TrendingDown, DollarSign, Activity, AlertTriangle, Target } from 'lucide-react'
import { AnalyticsService, type AdvancedAnalytics } from '@/lib/services/analytics-service'
import { getCurrentUser } from '@/lib/auth'

export function AnalyticsDashboard() {
  const [analytics, setAnalytics] = useState<AdvancedAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState('30d')

  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        const user = await getCurrentUser()
        if (!user) return
        
        const data = await AnalyticsService.getAdvancedAnalytics(user.id, period)
        setAnalytics(data)
      } catch (error) {
        console.error('Failed to load analytics:', error)
      } finally {
        setLoading(false)
      }
    }

    loadAnalytics()
  }, [period])

  if (loading) {
    return <div className="flex items-center justify-center py-8">Loading analytics...</div>
  }

  if (!analytics) {
    return <div className="flex items-center justify-center py-8">No analytics data available</div>
  }

  const getRiskColor = (score: number) => {
    if (score < 30) return 'text-green-600'
    if (score < 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'bg-green-100 text-green-800'
      case 'B': return 'bg-blue-100 text-blue-800'
      case 'C': return 'bg-yellow-100 text-yellow-800'
      case 'D': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Analytics Overview</h2>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">7 Days</SelectItem>
            <SelectItem value="30d">30 Days</SelectItem>
            <SelectItem value="90d">90 Days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Volume</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${analytics.volumeMetrics.totalVolume.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics.volumeMetrics.transactionCount} transactions
            </p>
            <div className="flex items-center mt-2">
              {analytics.volumeMetrics.monthlyGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
              )}
              <span className={`text-xs ${analytics.volumeMetrics.monthlyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(analytics.volumeMetrics.monthlyGrowth).toFixed(1)}% from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              MWK {analytics.profitMetrics.netProfit.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics.profitMetrics.profitMargin.toFixed(2)}% margin
            </p>
            <div className="mt-2">
              <Badge className={getGradeColor(analytics.performanceMetrics.profitMarginGrade)}>
                Grade {analytics.performanceMetrics.profitMarginGrade}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getRiskColor(analytics.riskMetrics.overallRiskScore)}`}>
              {analytics.riskMetrics.overallRiskScore.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Overall risk assessment
            </p>
            <Progress 
              value={analytics.riskMetrics.overallRiskScore} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.performanceMetrics.overallScore.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Overall performance score
            </p>
            <div className="flex gap-1 mt-2">
              <Badge size="sm" className={getGradeColor(analytics.performanceMetrics.profitMarginGrade)}>
                P: {analytics.performanceMetrics.profitMarginGrade}
              </Badge>
              <Badge size="sm" className={getGradeColor(analytics.performanceMetrics.volumeGrowthGrade)}>
                V: {analytics.performanceMetrics.volumeGrowthGrade}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Volume Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Volume Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm">Daily Average</span>
              <span className="font-medium">${analytics.volumeMetrics.dailyAverage.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Avg Transaction Size</span>
              <span className="font-medium">${analytics.volumeMetrics.averageTransactionSize.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Weekly Growth</span>
              <span className={`font-medium ${analytics.volumeMetrics.weeklyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {analytics.volumeMetrics.weeklyGrowth.toFixed(1)}%
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Risk Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Risk Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Volatility</span>
                <span>{analytics.riskMetrics.volatilityScore.toFixed(1)}</span>
              </div>
              <Progress value={analytics.riskMetrics.volatilityScore} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Liquidity Ratio</span>
                <span>{analytics.riskMetrics.liquidityRatio.toFixed(1)}%</span>
              </div>
              <Progress value={analytics.riskMetrics.liquidityRatio} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Concentration Risk</span>
                <span>{analytics.riskMetrics.concentrationRisk.toFixed(1)}%</span>
              </div>
              <Progress value={analytics.riskMetrics.concentrationRisk} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Forecasting */}
      <Card>
        <CardHeader>
          <CardTitle>6-Month Forecast</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
            {analytics.forecasting.map((forecast, index) => (
              <div key={index} className="text-center p-3 border rounded-lg">
                <p className="text-sm font-medium">{forecast.period}</p>
                <p className="text-lg font-bold">${forecast.projectedVolume.toFixed(0)}</p>
                <p className="text-xs text-muted-foreground">
                  Profit: MWK {forecast.projectedProfit.toFixed(0)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {forecast.confidence}% confidence
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
