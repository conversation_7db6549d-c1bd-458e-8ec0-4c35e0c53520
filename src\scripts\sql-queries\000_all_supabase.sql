-- Combined schema and policies for Supabase
-- Order: users → transactions → exchange_rates → financial_metrics → audit_logs → compliance_records → operator_locations → views/functions → profits → RLS

-- ===== 001_create-users_table.sql =====
-- Created users table for authentication and role management
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    role VARCHAR(20) NOT NULL CHECK (LOWER(role) IN ('supplier', 'operator')),
    name VARCHAR(255),
    nick_name TEXT,
    phone VARCHAR(50),
    address TEXT,
    tier VARCHAR(20) DEFAULT 'basic' CHECK (tier IN ('basic', 'premium', 'enterprise')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert demo users
INSERT INTO users (email, password_hash, role, name, nick_name, tier, email_verified) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'supplier', 'John Supplier', 'John', 'enterprise', TRUE),
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'operator', 'Jane Operator', 'Jane', 'premium', TRUE)
ON CONFLICT (email) DO NOTHING;


-- ===== 002_create_transaction_table.sql =====
-- Created transactions table for transaction management aligned with app
CREATE TABLE IF NOT EXISTS transactions (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('supplier_to_operator','operator_to_customer','customer_to_operator','settlement')),
    usdt_amount NUMERIC NOT NULL,
    mwk_amount NUMERIC NOT NULL,
    rate_used NUMERIC NOT NULL,
    txid TEXT,
    counterparty TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions(timestamp);

-- updated_at trigger
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();


-- ===== 003_create_exchange_rates_table.sql =====
-- Created exchange rates table for historical rate tracking
CREATE TABLE IF NOT EXISTS exchange_rates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    currency VARCHAR(10) NOT NULL CHECK (currency IN ('USDC', 'USDT', 'BUSD', 'DAI')),
    base_rate DECIMAL(15, 8) NOT NULL,
    buy_rate DECIMAL(15, 8) NOT NULL,
    sell_rate DECIMAL(15, 8) NOT NULL,
    spread DECIMAL(8, 6) NOT NULL,
    source VARCHAR(50) NOT NULL DEFAULT 'coingecko',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    volume_24h DECIMAL(20, 4),
    change_24h DECIMAL(8, 4),
    market_cap DECIMAL(20, 4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency ON exchange_rates(currency);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_timestamp ON exchange_rates(timestamp);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_active ON exchange_rates(is_active);
CREATE INDEX IF NOT EXISTS idx_exchange_rates_currency_timestamp ON exchange_rates(currency, timestamp DESC);

CREATE UNIQUE INDEX IF NOT EXISTS idx_exchange_rates_currency_timestamp_unique 
ON exchange_rates(currency, date_trunc('minute', timestamp));

INSERT INTO exchange_rates (currency, base_rate, buy_rate, sell_rate, spread) VALUES
('USDC', 1.0000, 0.9990, 1.0010, 0.0010),
('USDT', 1.0000, 0.9985, 1.0015, 0.0015),
('BUSD', 1.0000, 0.9988, 1.0012, 0.0012),
('DAI', 1.0000, 0.9992, 1.0008, 0.0008)
ON CONFLICT DO NOTHING;


-- ===== 004_create_financial_metrics_table.sql =====
-- Created financial metrics table for performance tracking
CREATE TABLE IF NOT EXISTS financial_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(20, 4) NOT NULL,
    currency VARCHAR(10),
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    calculation_method VARCHAR(50),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_financial_metrics_user_id ON financial_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_financial_metrics_type ON financial_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_financial_metrics_name ON financial_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_financial_metrics_period ON financial_metrics(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_financial_metrics_created_at ON financial_metrics(created_at);

CREATE INDEX IF NOT EXISTS idx_financial_metrics_user_type_period 
ON financial_metrics(user_id, metric_type, period_start DESC);


-- ===== 005_create_audit_logs_table.sql =====
-- Created audit logs table for compliance and security tracking
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_success ON audit_logs(success);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action_date 
ON audit_logs(user_id, action, created_at DESC);


-- ===== 006_create_compliance_records.sql =====
-- Created compliance records table for AML and regulatory compliance
CREATE TABLE IF NOT EXISTS compliance_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id VARCHAR(50) REFERENCES transactions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    compliance_type VARCHAR(50) NOT NULL CHECK (compliance_type IN ('AML', 'KYC', 'CTR', 'SAR')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'under_review')),
    risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
    risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    verification_method VARCHAR(50),
    verification_documents JSONB DEFAULT '[]'::jsonb,
    verification_notes TEXT,
    reviewer_id UUID REFERENCES users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reporting_required BOOLEAN DEFAULT FALSE,
    reporting_deadline TIMESTAMP WITH TIME ZONE,
    reported_at TIMESTAMP WITH TIME ZONE,
    reporting_reference VARCHAR(255),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_compliance_records_transaction_id ON compliance_records(transaction_id);
CREATE INDEX IF NOT EXISTS idx_compliance_records_user_id ON compliance_records(user_id);
CREATE INDEX IF NOT EXISTS idx_compliance_records_type ON compliance_records(compliance_type);
CREATE INDEX IF NOT EXISTS idx_compliance_records_status ON compliance_records(status);
CREATE INDEX IF NOT EXISTS idx_compliance_records_risk_level ON compliance_records(risk_level);
CREATE INDEX IF NOT EXISTS idx_compliance_records_created_at ON compliance_records(created_at);

CREATE TRIGGER update_compliance_records_updated_at 
    BEFORE UPDATE ON compliance_records 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();


-- ===== 007_create_operator_locations_table.sql =====
-- Created operator locations table for tracking vending machine locations
CREATE TABLE IF NOT EXISTS operator_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    country VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_type VARCHAR(50) CHECK (location_type IN ('mall', 'airport', 'station', 'store', 'kiosk', 'other')),
    operating_hours JSONB DEFAULT '{}'::jsonb,
    contact_phone VARCHAR(50),
    contact_email VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    daily_limit DECIMAL(15, 4) DEFAULT 10000,
    current_balance DECIMAL(15, 4) DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_operator_locations_operator_id ON operator_locations(operator_id);
CREATE INDEX IF NOT EXISTS idx_operator_locations_status ON operator_locations(status);
CREATE INDEX IF NOT EXISTS idx_operator_locations_city ON operator_locations(city);
CREATE INDEX IF NOT EXISTS idx_operator_locations_country ON operator_locations(country);
CREATE INDEX IF NOT EXISTS idx_operator_locations_type ON operator_locations(location_type);

CREATE TRIGGER update_operator_locations_updated_at 
    BEFORE UPDATE ON operator_locations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();


-- ===== 008_create_view_functions.sql =====
-- Created useful views and functions for reporting and analytics

-- View for transaction summary by operator
CREATE OR REPLACE VIEW operator_transaction_summary AS
SELECT 
    u.id as operator_id,
    u.name as operator_name,
    u.email as operator_email,
    COUNT(t.id) as total_transactions,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_transactions,
    COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_transactions,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.amount END), 0) as total_volume,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.operator_commission END), 0) as total_commission,
    COALESCE(AVG(CASE WHEN t.status = 'completed' THEN t.amount END), 0) as avg_transaction_amount,
    ROUND(
        CASE 
            WHEN COUNT(t.id) > 0 THEN 
                (COUNT(CASE WHEN t.status = 'completed' THEN 1 END)::decimal / COUNT(t.id)) * 100 
            ELSE 0 
        END, 2
    ) as success_rate
FROM users u
LEFT JOIN transactions t ON u.id = t.operator_id
WHERE u.role = 'operator'
GROUP BY u.id, u.name, u.email;

-- View for supplier revenue summary
CREATE OR REPLACE VIEW supplier_revenue_summary AS
SELECT 
    u.id as supplier_id,
    u.name as supplier_name,
    u.email as supplier_email,
    COUNT(t.id) as total_transactions,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_transactions,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.amount END), 0) as total_volume,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.supplier_fee END), 0) as total_revenue,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_fees END), 0) as total_fees_collected,
    COUNT(DISTINCT t.operator_id) as active_operators
FROM users u
LEFT JOIN transactions t ON u.id = t.supplier_id
WHERE u.role = 'supplier'
GROUP BY u.id, u.name, u.email;

-- View for daily transaction metrics
CREATE OR REPLACE VIEW daily_transaction_metrics AS
SELECT 
    DATE(t.created_at) as transaction_date,
    t.currency,
    t.transaction_type,
    COUNT(*) as transaction_count,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_count,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.amount END), 0) as total_volume,
    COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.total_fees END), 0) as total_fees,
    COALESCE(AVG(CASE WHEN t.status = 'completed' THEN t.amount END), 0) as avg_amount
FROM transactions t
GROUP BY DATE(t.created_at), t.currency, t.transaction_type
ORDER BY transaction_date DESC, t.currency, t.transaction_type;

-- Function to calculate operator performance metrics
CREATE OR REPLACE FUNCTION calculate_operator_performance(
    p_operator_id UUID,
    p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    p_end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    total_transactions BIGINT,
    completed_transactions BIGINT,
    total_volume DECIMAL(15,4),
    total_commission DECIMAL(15,4),
    success_rate DECIMAL(5,2),
    avg_transaction_amount DECIMAL(15,4),
    daily_avg_transactions DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(t.id)::BIGINT,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END)::BIGINT,
        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.amount END), 0),
        COALESCE(SUM(CASE WHEN t.status = 'completed' THEN t.operator_commission END), 0),
        ROUND(
            CASE 
                WHEN COUNT(t.id) > 0 THEN 
                    (COUNT(CASE WHEN t.status = 'completed' THEN 1 END)::decimal / COUNT(t.id)) * 100 
                ELSE 0 
            END, 2
        ),
        COALESCE(AVG(CASE WHEN t.status = 'completed' THEN t.amount END), 0),
        ROUND(COUNT(t.id)::decimal / GREATEST(EXTRACT(days FROM p_end_date - p_start_date), 1), 2)
    FROM transactions t
    WHERE t.operator_id = p_operator_id
    AND t.created_at >= p_start_date
    AND t.created_at <= p_end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get exchange rate at specific time
CREATE OR REPLACE FUNCTION get_exchange_rate_at_time(
    p_currency VARCHAR(10),
    p_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS DECIMAL(15,8) AS $$
DECLARE
    rate DECIMAL(15,8);
BEGIN
    SELECT base_rate INTO rate
    FROM exchange_rates
    WHERE currency = p_currency
    AND timestamp <= p_timestamp
    AND is_active = TRUE
    ORDER BY timestamp DESC
    LIMIT 1;
    
    RETURN COALESCE(rate, 1.0);
END;
$$ LANGUAGE plpgsql;

-- Function to calculate risk score for transaction
CREATE OR REPLACE FUNCTION calculate_transaction_risk_score(
    p_transaction_id VARCHAR(50)
)
RETURNS INTEGER AS $$
DECLARE
    risk_score INTEGER := 0;
    tx_amount DECIMAL(15,4);
    user_tx_count INTEGER;
    daily_volume DECIMAL(15,4);
BEGIN
    -- Get transaction details
    SELECT amount INTO tx_amount
    FROM transactions
    WHERE id = p_transaction_id;
    
    -- Amount-based risk (higher amounts = higher risk)
    IF tx_amount > 5000 THEN
        risk_score := risk_score + 30;
    ELSIF tx_amount > 1000 THEN
        risk_score := risk_score + 15;
    ELSIF tx_amount > 500 THEN
        risk_score := risk_score + 5;
    END IF;
    
    -- User transaction history risk
    SELECT COUNT(*) INTO user_tx_count
    FROM transactions t1
    JOIN transactions t2 ON t1.operator_id = t2.operator_id
    WHERE t2.id = p_transaction_id
    AND t1.created_at >= NOW() - INTERVAL '30 days';
    
    IF user_tx_count < 5 THEN
        risk_score := risk_score + 20;
    ELSIF user_tx_count < 20 THEN
        risk_score := risk_score + 10;
    END IF;
    
    -- Daily volume risk
    SELECT COALESCE(SUM(amount), 0) INTO daily_volume
    FROM transactions t1
    JOIN transactions t2 ON t1.operator_id = t2.operator_id
    WHERE t2.id = p_transaction_id
    AND t1.created_at >= CURRENT_DATE
    AND t1.status = 'completed';
    
    IF daily_volume > 10000 THEN
        risk_score := risk_score + 25;
    ELSIF daily_volume > 5000 THEN
        risk_score := risk_score + 10;
    END IF;
    
    RETURN LEAST(risk_score, 100);
END;
$$ LANGUAGE plpgsql;


-- ===== 010_create_profits_table.sql =====
-- Created profits table to match app expectations
CREATE TABLE IF NOT EXISTS profits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    week_id TEXT NOT NULL,
    total_usdt_received NUMERIC NOT NULL DEFAULT 0,
    total_mwk_collected NUMERIC NOT NULL DEFAULT 0,
    gross_profit NUMERIC NOT NULL DEFAULT 0,
    supplier_share NUMERIC NOT NULL DEFAULT 0,
    operator_share NUMERIC NOT NULL DEFAULT 0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_profits_user_id ON profits(user_id);
CREATE INDEX IF NOT EXISTS idx_profits_week_id ON profits(week_id);
CREATE INDEX IF NOT EXISTS idx_profits_timestamp ON profits(timestamp);

ALTER TABLE profits ENABLE ROW LEVEL SECURITY;


-- ===== 009_create_row_level_security.sql =====
-- Created Row Level Security policies for data protection
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE operator_locations ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY users_select_own ON users
    FOR SELECT
    USING (auth.uid()::text = id::text);

CREATE POLICY users_update_own ON users
    FOR UPDATE
    USING (auth.uid()::text = id::text);

-- Transactions table policies
CREATE POLICY transactions_select_own ON transactions
    FOR SELECT
    USING (
        auth.uid()::text = user_id::text
    );

CREATE POLICY transactions_insert_own ON transactions
    FOR INSERT
    WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY transactions_update_own ON transactions
    FOR UPDATE
    USING (
        auth.uid()::text = user_id::text
    );

-- Exchange rates table policies (read-only for all authenticated users)
CREATE POLICY exchange_rates_select_all ON exchange_rates
    FOR SELECT
    USING (auth.role() = 'authenticated');

-- Financial metrics table policies
CREATE POLICY financial_metrics_select_own ON financial_metrics
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

CREATE POLICY financial_metrics_insert_own ON financial_metrics
    FOR INSERT
    WITH CHECK (auth.uid()::text = user_id::text);

-- Audit logs table policies (read-only for own records)
CREATE POLICY audit_logs_select_own ON audit_logs
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

-- Compliance records table policies
CREATE POLICY compliance_records_select_own ON compliance_records
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

-- Operator locations table policies
CREATE POLICY operator_locations_select_own ON operator_locations
    FOR SELECT
    USING (auth.uid()::text = operator_id::text);

CREATE POLICY operator_locations_insert_own ON operator_locations
    FOR INSERT
    WITH CHECK (auth.uid()::text = operator_id::text);

CREATE POLICY operator_locations_update_own ON operator_locations
    FOR UPDATE
    USING (auth.uid()::text = operator_id::text);

CREATE POLICY operator_locations_delete_own ON operator_locations
    FOR DELETE
    USING (auth.uid()::text = operator_id::text);

-- Profits table policies
CREATE POLICY profits_select_own ON profits
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

CREATE POLICY profits_insert_own ON profits
    FOR INSERT
    WITH CHECK (auth.uid()::text = user_id::text);


