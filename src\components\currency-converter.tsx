"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { exchangeRateService } from "@/lib/services/exchange-rate-services"

export function CurrencyConverter() {
  const [amount, setAmount] = useState("")
  const [fromCurrency, setFromCurrency] = useState("USDC")
  const [toCurrency, setToCurrency] = useState("USDT")
  const [conversionType, setConversionType] = useState<"buy" | "sell">("buy")
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState("")

  const supportedCurrencies = exchangeRateService.getSupportedCurrencies()

  const handleConvert = () => {
    const numAmount = Number.parseFloat(amount)
    if (isNaN(numAmount) || numAmount <= 0) {
      setError("Please enter a valid amount")
      return
    }

    if (fromCurrency === toCurrency) {
      setError("Please select different currencies")
      return
    }

    try {
      const conversion = exchangeRateService.calculateConversion(numAmount, fromCurrency, toCurrency, conversionType)
      setResult(conversion)
      setError("")
    } catch (err: any) {
      setError(err.message || "Conversion failed")
      setResult(null)
    }
  }

  const swapCurrencies = () => {
    setFromCurrency(toCurrency)
    setToCurrency(fromCurrency)
    setResult(null)
  }

  return (
    <Card className="border-primary/20">
      <CardHeader>
        <CardTitle>Currency Converter</CardTitle>
        <CardDescription>Convert between supported stablecoins</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="border-primary/20 focus:border-primary"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Transaction Type</Label>
            <Select value={conversionType} onValueChange={(value: "buy" | "sell") => setConversionType(value)}>
              <SelectTrigger className="border-primary/20 focus:border-primary">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="buy">Buy</SelectItem>
                <SelectItem value="sell">Sell</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex-1 space-y-2">
            <Label htmlFor="from">From</Label>
            <Select value={fromCurrency} onValueChange={setFromCurrency}>
              <SelectTrigger className="border-primary/20 focus:border-primary">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {supportedCurrencies.map((currency) => (
                  <SelectItem key={currency} value={currency}>
                    {currency}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={swapCurrencies}
            className="mt-6 border-primary/20 bg-transparent"
          >
            ⇄
          </Button>

          <div className="flex-1 space-y-2">
            <Label htmlFor="to">To</Label>
            <Select value={toCurrency} onValueChange={setToCurrency}>
              <SelectTrigger className="border-primary/20 focus:border-primary">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {supportedCurrencies.map((currency) => (
                  <SelectItem key={currency} value={currency}>
                    {currency}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button
          onClick={handleConvert}
          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
          disabled={!amount}
        >
          Convert
        </Button>

        {error && (
          <Alert className="border-destructive/20 bg-destructive/5">
            <AlertDescription className="text-destructive">{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="p-4 bg-muted/50 rounded-lg space-y-3">
            <h4 className="font-medium">Conversion Result</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Exchange Rate</span>
                <Badge variant="secondary">
                  {result.rate} {fromCurrency}/{toCurrency}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Converted Amount</span>
                <span className="font-medium">
                  {result.convertedAmount} {toCurrency}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Conversion Fee</span>
                <span className="text-destructive">
                  -{result.fee} {toCurrency}
                </span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium">You Receive</span>
                  <span className="font-bold text-primary text-lg">
                    {result.total} {toCurrency}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default CurrencyConverter