// app/lib/types.ts
import type { AuthResponse, User as SupabaseUser } from "@supabase/supabase-js";
import type React from "react";

// Shared interfaces
export interface User {
    id: string;
    email: string;
    name: string;
    role: "supplier" | "operator";
    wallet_address?: string;
    created_at: string;
}

export interface Transaction {
    id: string;
    type: "supplier_to_operator" | "operator_to_customer" | "customer_to_operator" | "settlement";
    usdt_amount: number;
    mwk_amount: number;
    rate_used: number;
    txid?: string;
    counterparty: string;
    timestamp: string;
    user_id: string;
}

export interface Profit {
    id: string;
    week_id: string;
    total_usdt_received: number;
    total_mwk_collected: number;
    gross_profit: number;
    supplier_share: number;
    operator_share: number;
    timestamp: string;
}

// Auth function types
export type GetCurrentUserReturn = Promise<User | null>;
export type LogoutReturn = Promise<void>;
export type GetTransactionsReturn = Promise<Transaction[]>;
export type AddTransactionInput = Omit<Transaction, "id" | "timestamp">;
export type AddTransactionReturn = Promise<Transaction | null>;
export type GetProfitsReturn = Promise<Profit[]>;
export type ExchangeRates = {
    usd_mwk: number;
    usd_zar: number;
    black_market_rate: number;
    last_updated: string;
    source: string;
};
export type GetExchangeRatesReturn = Promise<ExchangeRates>;
export type USDTBalance = {
    balance: number;
    address: string;
    trx_balance: number;
    last_updated: string;
};
export type GetUSDTBalanceReturn = Promise<USDTBalance>;

// Component-specific types (used in operator/page.tsx)
export interface OperatorTab {
    id: string;
    label: string;
    icon: React.ReactNode;
}

export type ActiveTab = string;

export type NewTransaction = {
    customer: string;
    type: "buy" | "sell";
    usdtAmount: string;
    mwkAmount: string;
    rate: string;
};

export type Calculation = {
    usdtAmount: string;
    buyRate: string;
    sellRate: string;
    supplierShare: string;
    operatorShare: string;
};

export interface FinancialDashboardProps {
    userRole: "operator";
    userId: string;
}

export interface SendUSDTDialogProps {
    recipientAddress: string;
    recipientLabel: string;
    fromAddress: string;
    currentBalance: number;
    trxBalance: number;
}

export interface ExportDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    userRole: "operator";
}

// Login page types (used in app/page.tsx)
export type Role = "Supplier" | "Operator";
export type UserRow = {
    role: "Supplier" | "Operator";
};


export interface User {
    id: string;
    email: string;
    name: string;
    role: "supplier" | "operator";
    wallet_address?: string;
    created_at: string;
}


