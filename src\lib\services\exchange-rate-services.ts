"use client"

export interface ExchangeRate {
    currency: string
    rate: number
    lastUpdated: Date
    spread: number
    buyRate: number
    sellRate: number
}

export interface ExchangeRateResponse {
    success: boolean
    rates: Record<string, ExchangeRate>
    error?: string
}

class ExchangeRateService {
    private rates: Record<string, ExchangeRate> = {}
    private listeners: Array<(rates: Record<string, ExchangeRate>) => void> = []
    private updateInterval: NodeJS.Timeout | null = null
    private lastFetch: Date | null = null
    private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

    // Supported stablecoins with their spreads
    private readonly SUPPORTED_CURRENCIES = {
        USDC: { spread: 0.001, name: "USD Coin" },
        USDT: { spread: 0.0015, name: "Tether" },
        BUSD: { spread: 0.0012, name: "Binance USD" },
        DAI: { spread: 0.0008, name: "Dai Stablecoin" },
    }

    constructor() {
        this.initializeRates()
        this.startPeriodicUpdates()
    }

    private initializeRates() {
        // Initialize with default rates
        Object.keys(this.SUPPORTED_CURRENCIES).forEach((currency) => {
            this.rates[currency] = {
                currency,
                rate: 1.0,
                lastUpdated: new Date(),
                spread: this.SUPPORTED_CURRENCIES[currency as keyof typeof this.SUPPORTED_CURRENCIES].spread,
                buyRate: 1.0 - this.SUPPORTED_CURRENCIES[currency as keyof typeof this.SUPPORTED_CURRENCIES].spread,
                sellRate: 1.0 + this.SUPPORTED_CURRENCIES[currency as keyof typeof this.SUPPORTED_CURRENCIES].spread,
            }
        })
    }

    private startPeriodicUpdates() {
        // Update rates every 5 minutes
        this.updateInterval = setInterval(() => {
            this.fetchRates()
        }, this.CACHE_DURATION)

        // Initial fetch
        this.fetchRates()
    }

    private async fetchRates(): Promise<void> {
        try {
            // Using a free exchange rate API (CoinGecko API)
            const response = await fetch(
                "https://api.coingecko.com/api/v3/simple/price?ids=usd-coin,tether,binance-usd,dai&vs_currencies=usd&include_last_updated_at=true",
                {
                    headers: {
                        Accept: "application/json",
                    },
                },
            )

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()

            // Map API response to our format
            const currencyMap = {
                "usd-coin": "USDC",
                tether: "USDT",
                "binance-usd": "BUSD",
                dai: "DAI",
            }

            Object.entries(data).forEach(([apiKey, value]: [string, any]) => {
                const currency = currencyMap[apiKey as keyof typeof currencyMap]
                if (currency && this.SUPPORTED_CURRENCIES[currency as keyof typeof this.SUPPORTED_CURRENCIES]) {
                    const spread = this.SUPPORTED_CURRENCIES[currency as keyof typeof this.SUPPORTED_CURRENCIES].spread
                    const rate = value.usd || 1.0

                    this.rates[currency] = {
                        currency,
                        rate,
                        lastUpdated: new Date(value.last_updated_at * 1000 || Date.now()),
                        spread,
                        buyRate: rate - rate * spread,
                        sellRate: rate + rate * spread,
                    }
                }
            })

            this.lastFetch = new Date()
            this.notifyListeners()

            console.log("[v0] Exchange rates updated successfully")
        } catch (error) {
            console.error("[v0] Failed to fetch exchange rates:", error)

            // Fallback to simulated rates with small variations
            this.simulateRateChanges()
        }
    }

    private simulateRateChanges() {
        // Simulate small rate changes for demo purposes
        Object.keys(this.rates).forEach((currency) => {
            const currentRate = this.rates[currency].rate
            const variation = (Math.random() - 0.5) * 0.002 // ±0.1% variation
            const newRate = Math.max(0.995, Math.min(1.005, currentRate + variation))
            const spread = this.rates[currency].spread

            this.rates[currency] = {
                ...this.rates[currency],
                rate: Number(newRate.toFixed(4)),
                buyRate: Number((newRate - newRate * spread).toFixed(4)),
                sellRate: Number((newRate + newRate * spread).toFixed(4)),
                lastUpdated: new Date(),
            }
        })

        this.lastFetch = new Date()
        this.notifyListeners()
    }

    subscribe(listener: (rates: Record<string, ExchangeRate>) => void): () => void {
        this.listeners.push(listener)
        // Immediately call with current rates
        listener({ ...this.rates })

        return () => {
            this.listeners = this.listeners.filter((l) => l !== listener)
        }
    }

    private notifyListeners() {
        this.listeners.forEach((listener) => listener({ ...this.rates }))
    }

    getRates(): Record<string, ExchangeRate> {
        return { ...this.rates }
    }

    getRate(currency: string): ExchangeRate | null {
        return this.rates[currency] || null
    }

    getBuyRate(currency: string): number {
        const rate = this.rates[currency]
        return rate ? rate.buyRate : 1.0
    }

    getSellRate(currency: string): number {
        const rate = this.rates[currency]
        return rate ? rate.sellRate : 1.0
    }

    isStale(): boolean {
        if (!this.lastFetch) return true
        return Date.now() - this.lastFetch.getTime() > this.CACHE_DURATION
    }

    async forceRefresh(): Promise<void> {
        await this.fetchRates()
    }

    getLastUpdateTime(): Date | null {
        return this.lastFetch
    }

    getSupportedCurrencies(): string[] {
        return Object.keys(this.SUPPORTED_CURRENCIES)
    }

    calculateConversion(
        amount: number,
        fromCurrency: string,
        toCurrency: string,
        type: "buy" | "sell" = "buy",
    ): {
        convertedAmount: number
        rate: number
        fee: number
        total: number
    } {
        const fromRate = this.getRate(fromCurrency)
        const toRate = this.getRate(toCurrency)

        if (!fromRate || !toRate) {
            throw new Error(`Unsupported currency conversion: ${fromCurrency} to ${toCurrency}`)
        }

        const rate = type === "buy" ? fromRate.buyRate : fromRate.sellRate
        const convertedAmount = amount * rate
        const fee = convertedAmount * 0.001 // 0.1% conversion fee
        const total = convertedAmount - fee

        return {
            convertedAmount: Number(convertedAmount.toFixed(4)),
            rate: Number(rate.toFixed(4)),
            fee: Number(fee.toFixed(4)),
            total: Number(total.toFixed(4)),
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval)
            this.updateInterval = null
        }
        this.listeners = []
    }
}

// Export singleton instance
export const exchangeRateService = new ExchangeRateService()
