import { db, settlements, transactions, users } from '@/lib/db'
import { eq, and, desc, sum, count } from 'drizzle-orm'
import { getTronWalletInfo, validateTronAddress } from '@/lib/web3'

export interface SettlementRequest {
  amount: number
  recipient: string
  notes?: string
  userId: string
}

export interface SettlementAnalytics {
  totalSettled: number
  pendingAmount: number
  completedCount: number
  averageProcessingTime: number
  successRate: number
}

export class SettlementService {
  static async createSettlement(request: SettlementRequest) {
    if (!validateTronAddress(request.recipient)) {
      throw new Error('Invalid TRON wallet address')
    }

    const [settlement] = await db.insert(settlements).values({
      amount: request.amount.toString(),
      recipient: request.recipient,
      notes: request.notes,
      userId: request.userId,
      status: 'pending'
    }).returning()

    return settlement
  }

  static async processSettlement(settlementId: string, txid: string) {
    const [updated] = await db.update(settlements)
      .set({ 
        status: 'processing',
        txid,
        processedAt: new Date()
      })
      .where(eq(settlements.id, settlementId))
      .returning()

    // Simulate blockchain confirmation
    setTimeout(async () => {
      await db.update(settlements)
        .set({ status: 'completed' })
        .where(eq(settlements.id, settlementId))
    }, 30000) // 30 seconds

    return updated
  }

  static async getSettlements(userId: string) {
    return await db.select()
      .from(settlements)
      .where(eq(settlements.userId, userId))
      .orderBy(desc(settlements.createdAt))
  }

  static async getSettlementAnalytics(userId: string): Promise<SettlementAnalytics> {
    const allSettlements = await this.getSettlements(userId)
    
    const totalSettled = allSettlements
      .filter(s => s.status === 'completed')
      .reduce((sum, s) => sum + parseFloat(s.amount), 0)

    const pendingAmount = allSettlements
      .filter(s => s.status === 'pending')
      .reduce((sum, s) => sum + parseFloat(s.amount), 0)

    const completedCount = allSettlements.filter(s => s.status === 'completed').length
    const totalCount = allSettlements.length

    // Calculate average processing time
    const completedSettlements = allSettlements.filter(s => s.status === 'completed' && s.processedAt)
    const avgProcessingTime = completedSettlements.length > 0 
      ? completedSettlements.reduce((sum, s) => {
          const processingTime = new Date(s.processedAt!).getTime() - new Date(s.createdAt).getTime()
          return sum + processingTime
        }, 0) / completedSettlements.length / (1000 * 60 * 60) // Convert to hours
      : 0

    return {
      totalSettled,
      pendingAmount,
      completedCount,
      averageProcessingTime: avgProcessingTime,
      successRate: totalCount > 0 ? (completedCount / totalCount) * 100 : 0
    }
  }

  static async validateSettlement(settlementId: string) {
    const settlement = await db.select()
      .from(settlements)
      .where(eq(settlements.id, settlementId))
      .limit(1)

    if (!settlement[0]) throw new Error('Settlement not found')

    if (settlement[0].txid) {
      // Verify on blockchain
      const walletInfo = await getTronWalletInfo(settlement[0].recipient)
      const recentTx = walletInfo.transactions.find(tx => tx.txid === settlement[0].txid)
      
      if (recentTx && recentTx.status === 'confirmed') {
        await db.update(settlements)
          .set({ status: 'completed' })
          .where(eq(settlements.id, settlementId))
        return true
      }
    }

    return false
  }
}