"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { FinancialCalculationsEngine } from "@/lib/financial-calculations"

export function TransactionCalculator() {
    const [amount, setAmount] = useState("")
    const [currency, setCurrency] = useState<"USDC" | "USDT" | "BUSD" | "DAI">("USDC")
    const [userTier, setUserTier] = useState<"basic" | "premium" | "enterprise">("basic")
    const [calculation, setCalculation] = useState<any>(null)
    const [validation, setValidation] = useState<any>(null)

    const handleCalculate = () => {
        const numAmount = Number.parseFloat(amount)
        if (isNaN(numAmount) || numAmount <= 0) return

        const fees = FinancialCalculationsEngine.calculateTransactionFees(numAmount, currency)
        const validationResult = FinancialCalculationsEngine.validateTransaction(numAmount, currency, userTier)

        setCalculation(fees)
        setValidation(validationResult)
    }

    return (
        <Card className="border-primary/20">
            <CardHeader>
                <CardTitle>Transaction Calculator</CardTitle>
                <CardDescription>Calculate fees and validate transaction limits</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="calc-amount">Amount (USD)</Label>
                        <Input
                            id="calc-amount"
                            type="number"
                            placeholder="Enter amount"
                            value={amount}
                            onChange={(e) => setAmount(e.target.value)}
                            className="border-primary/20 focus:border-primary"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="calc-currency">Currency</Label>
                        <Select value={currency} onValueChange={(value: any) => setCurrency(value)}>
                            <SelectTrigger className="border-primary/20 focus:border-primary">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="USDC">USDC</SelectItem>
                                <SelectItem value="USDT">USDT</SelectItem>
                                <SelectItem value="BUSD">BUSD</SelectItem>
                                <SelectItem value="DAI">DAI</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="calc-tier">User Tier</Label>
                        <Select value={userTier} onValueChange={(value: any) => setUserTier(value)}>
                            <SelectTrigger className="border-primary/20 focus:border-primary">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="basic">Basic</SelectItem>
                                <SelectItem value="premium">Premium</SelectItem>
                                <SelectItem value="enterprise">Enterprise</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <Button
                    onClick={handleCalculate}
                    className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                    disabled={!amount}
                >
                    Calculate Fees
                </Button>

                {validation && !validation.isValid && (
                    <Alert className="border-destructive/20 bg-destructive/5">
                        <AlertDescription>
                            <div className="space-y-1">
                                {validation.errors.map((error: string, index: number) => (
                                    <div key={index} className="text-destructive">
                                        {error}
                                    </div>
                                ))}
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {validation && validation.warnings.length > 0 && (
                    <Alert className="border-yellow-500/20 bg-yellow-500/5">
                        <AlertDescription>
                            <div className="space-y-1">
                                {validation.warnings.map((warning: string, index: number) => (
                                    <div key={index} className="text-yellow-700">
                                        {warning}
                                    </div>
                                ))}
                            </div>
                        </AlertDescription>
                    </Alert>
                )}

                {calculation && validation?.isValid && (
                    <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                        <h4 className="font-medium">Fee Breakdown</h4>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Supplier Fee</span>
                                <Badge variant="secondary">${calculation.supplierFee}</Badge>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Operator Commission</span>
                                <Badge variant="secondary">${calculation.operatorCommission}</Badge>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm text-muted-foreground">Network Fee</span>
                                <Badge variant="secondary">${calculation.networkFee}</Badge>
                            </div>
                            <div className="flex justify-between">
                                <span className="font-medium">Total Fees</span>
                                <Badge className="bg-primary text-primary-foreground">${calculation.totalFees}</Badge>
                            </div>
                        </div>
                        <div className="border-t pt-2">
                            <div className="flex justify-between items-center">
                                <span className="font-medium">Amount After Fees</span>
                                <span className="font-bold text-primary">
                                    ${(Number.parseFloat(amount) - calculation.totalFees).toFixed(2)}
                                </span>
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
