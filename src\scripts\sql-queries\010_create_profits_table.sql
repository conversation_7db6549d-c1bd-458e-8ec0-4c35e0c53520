-- Created profits table to match app expectations
CREATE TABLE IF NOT EXISTS profits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    week_id TEXT NOT NULL,
    total_usdt_received NUMERIC NOT NULL DEFAULT 0,
    total_mwk_collected NUMERIC NOT NULL DEFAULT 0,
    gross_profit NUMERIC NOT NULL DEFAULT 0,
    supplier_share NUMERIC NOT NULL DEFAULT 0,
    operator_share NUMERIC NOT NULL DEFAULT 0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_profits_user_id ON profits(user_id);
CREATE INDEX IF NOT EXISTS idx_profits_week_id ON profits(week_id);
CREATE INDEX IF NOT EXISTS idx_profits_timestamp ON profits(timestamp);

-- Enable RLS
ALTER TABLE profits ENABLE ROW LEVEL SECURITY;

