import { pgTable, uuid, varchar, decimal, timestamp, text, integer, boolean } from 'drizzle-orm/pg-core'
import { relations } from 'drizzle-orm'

export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  nickname: varchar('nick_name', { length: 100 }),
  role: varchar('role', { length: 20 }).notNull(),
  walletAddress: varchar('wallet_address', { length: 100 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
})

export const transactions = pgTable('transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  type: varchar('type', { length: 50 }).notNull(),
  usdtAmount: decimal('usdt_amount', { precision: 18, scale: 6 }).notNull(),
  mwkAmount: decimal('mwk_amount', { precision: 18, scale: 2 }).notNull(),
  rateUsed: decimal('rate_used', { precision: 10, scale: 4 }).notNull(),
  txid: varchar('txid', { length: 100 }),
  counterparty: varchar('counterparty', { length: 255 }).notNull(),
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  userId: uuid('user_id').notNull().references(() => users.id),
  status: varchar('status', { length: 20 }).default('pending'),
})

export const settlements = pgTable('settlements', {
  id: uuid('id').primaryKey().defaultRandom(),
  amount: decimal('amount', { precision: 18, scale: 6 }).notNull(),
  recipient: varchar('recipient', { length: 100 }).notNull(),
  status: varchar('status', { length: 20 }).default('pending'),
  txid: varchar('txid', { length: 100 }),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  processedAt: timestamp('processed_at'),
  userId: uuid('user_id').notNull().references(() => users.id),
})

export const profits = pgTable('profits', {
  id: uuid('id').primaryKey().defaultRandom(),
  weekId: varchar('week_id', { length: 20 }).notNull(),
  totalUsdtReceived: decimal('total_usdt_received', { precision: 18, scale: 6 }).notNull(),
  totalMwkCollected: decimal('total_mwk_collected', { precision: 18, scale: 2 }).notNull(),
  grossProfit: decimal('gross_profit', { precision: 18, scale: 2 }).notNull(),
  supplierShare: decimal('supplier_share', { precision: 18, scale: 2 }).notNull(),
  operatorShare: decimal('operator_share', { precision: 18, scale: 2 }).notNull(),
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  userId: uuid('user_id').notNull().references(() => users.id),
})

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  transactions: many(transactions),
  settlements: many(settlements),
  profits: many(profits),
}))

export const transactionsRelations = relations(transactions, ({ one }) => ({
  user: one(users, {
    fields: [transactions.userId],
    references: [users.id],
  }),
}))

export const settlementsRelations = relations(settlements, ({ one }) => ({
  user: one(users, {
    fields: [settlements.userId],
    references: [users.id],
  }),
}))

export const profitsRelations = relations(profits, ({ one }) => ({
  user: one(users, {
    fields: [profits.userId],
    references: [users.id],
  }),
}))

// Export types
export type User = typeof users.$inferSelect
export type NewUser = typeof users.$inferInsert
export type Transaction = typeof transactions.$inferSelect
export type NewTransaction = typeof transactions.$inferInsert
export type Settlement = typeof settlements.$inferSelect
export type NewSettlement = typeof settlements.$inferInsert
export type Profit = typeof profits.$inferSelect
export type NewProfit = typeof profits.$inferInsert
