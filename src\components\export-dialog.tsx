"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format as formatDate } from "date-fns"
import { CalendarIcon, Download, FileText, Table } from "lucide-react"
import { getCurrentUser, getTransactions } from "@/lib/auth"

interface ExportDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    userRole: "supplier" | "operator"
}

interface ExportData {
    dateRange: {
        start: string
        end: string
    }
    userRole: string
    transactions?: any[]
    profits?: any[]
    settlements?: any[]
}

// Mock export functions - replace with actual implementations
const exportToCSV = (data: any[], filename: string) => {
    console.log("Exporting to CSV:", filename, data)
    // Implement CSV export logic
}

const exportToPDF = (data: ExportData, filename: string) => {
    console.log("Exporting to PDF:", filename, data)
    // Implement PDF export logic
}

const generateSettlementReport = (transactions: any[], dateRange: { start: string; end: string }) => {
    return {
        period: `${dateRange.start} to ${dateRange.end}`,
        total_transactions: transactions.length,
        total_volume: transactions.reduce((sum, t) => sum + t.mwk_amount, 0),
        total_profit: transactions.reduce((sum, t) => sum + (t.mwk_amount * 0.025), 0),
    }
}

export function ExportDialog({ open, onOpenChange, userRole }: ExportDialogProps) {
    const [exportType, setExportType] = useState<"transactions" | "profits" | "settlements">("transactions")
    const [exportFormat, setExportFormat] = useState<"csv" | "pdf">("csv")
    const [dateRange, setDateRange] = useState({
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        end: new Date(),
    })

    const handleExport = async () => {
        try {
            const user = await getCurrentUser()
            const transactions = await getTransactions()

            if (!user) return

            const userTransactions = transactions.filter((t) => t.user_id === user.id)

            const exportData: ExportData = {
                dateRange: {
                    start: dateRange.start.toISOString(),
                    end: dateRange.end.toISOString(),
                },
                userRole,
            }

            const filename = `${userRole}_${exportType}_${formatDate(dateRange.start, "yyyy-MM-dd")}_to_${formatDate(dateRange.end, "yyyy-MM-dd")}`

            switch (exportType) {
                case "transactions":
                    const filteredTransactions = userTransactions.filter((t) => {
                        const transactionDate = new Date(t.timestamp)
                        return transactionDate >= dateRange.start && transactionDate <= dateRange.end
                    })
                    exportData.transactions = filteredTransactions

                    if (exportFormat === "csv") {
                        exportToCSV(filteredTransactions, filename)
                    } else {
                        exportToPDF(exportData, filename)
                    }
                    break

                case "profits":
                    const profitData = userTransactions.map((t) => ({
                        date: t.timestamp,
                        transaction_id: t.id,
                        counterparty: t.counterparty,
                        usdt_amount: t.usdt_amount,
                        mwk_amount: t.mwk_amount,
                        profit: t.mwk_amount * 0.025,
                        margin: (((t.mwk_amount * 0.025) / t.mwk_amount) * 100).toFixed(2) + "%",
                    }))
                    exportData.profits = profitData

                    if (exportFormat === "csv") {
                        exportToCSV(profitData, filename)
                    } else {
                        exportToPDF(exportData, filename)
                    }
                    break

                case "settlements":
                    const settlementReport = generateSettlementReport(userTransactions, {
                        start: dateRange.start.toISOString(),
                        end: dateRange.end.toISOString(),
                    })
                    exportData.settlements = [settlementReport]

                    if (exportFormat === "csv") {
                        exportToCSV([settlementReport], filename)
                    } else {
                        exportToPDF(exportData, filename)
                    }
                    break
            }

            onOpenChange(false)
        } catch (error) {
            console.error("Export error:", error)
        }
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Export Data</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <div>
                        <Label>Export Type</Label>
                        <Select value={exportType} onValueChange={(value: any) => setExportType(value)}>
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="transactions">Transactions</SelectItem>
                                <SelectItem value="profits">Profit Analysis</SelectItem>
                                <SelectItem value="settlements">Settlement Report</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div>
                        <Label>Format</Label>
                        <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                            <SelectTrigger>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="csv">
                                    <div className="flex items-center">
                                        <Table className="w-4 h-4 mr-2" />
                                        CSV
                                    </div>
                                </SelectItem>
                                <SelectItem value="pdf">
                                    <div className="flex items-center">
                                        <FileText className="w-4 h-4 mr-2" />
                                        PDF
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <Label>Start Date</Label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline" className="w-full justify-start text-left font-normal bg-transparent">
                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                        {formatDate(dateRange.start, "MMM dd, yyyy")}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                        mode="single"
                                        selected={dateRange.start}
                                        onSelect={(date) => date && setDateRange({ ...dateRange, start: date })}
                                        initialFocus
                                    />
                                </PopoverContent>
                            </Popover>
                        </div>
                        <div>
                            <Label>End Date</Label>
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline" className="w-full justify-start text-left font-normal bg-transparent">
                                        <CalendarIcon className="mr-2 h-4 w-4" />
                                        {formatDate(dateRange.end, "MMM dd, yyyy")}
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                        mode="single"
                                        selected={dateRange.end}
                                        onSelect={(date) => date && setDateRange({ ...dateRange, end: date })}
                                        initialFocus
                                    />
                                </PopoverContent>
                            </Popover>
                        </div>
                    </div>

                    <Button onClick={handleExport} className="w-full">
                        <Download className="w-4 h-4 mr-2" />
                        Export {exportType}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
