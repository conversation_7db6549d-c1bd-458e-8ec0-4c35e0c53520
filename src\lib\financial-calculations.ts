// Advanced financial calculations for TEVAVEND

export interface FinancialMetrics {
  totalVolume: number
  totalProfit: number
  profitMargin: number
  averageTransactionSize: number
  transactionCount: number
  dailyAverage: number
  weeklyGrowth: number
  monthlyGrowth: number
}

export interface ProfitBreakdown {
  grossProfit: number
  netProfit: number
  supplierShare: number
  operatorShare: number
  fees: number
  taxes: number
}

export interface RiskMetrics {
  volatilityScore: number
  liquidityRatio: number
  concentrationRisk: number
  operationalRisk: number
  overallRiskScore: number
}

export interface ForecastData {
  period: string
  projectedVolume: number
  projectedProfit: number
  confidence: number
}

// Calculate comprehensive financial metrics
export const calculateFinancialMetrics = (
  transactions: any[],
  period: "daily" | "weekly" | "monthly" = "monthly",
): FinancialMetrics => {
  const now = new Date()
  const periodStart = new Date()

  switch (period) {
    case "daily":
      periodStart.setDate(now.getDate() - 1)
      break
    case "weekly":
      periodStart.setDate(now.getDate() - 7)
      break
    case "monthly":
      periodStart.setMonth(now.getMonth() - 1)
      break
  }

  const periodTransactions = transactions.filter((t) => new Date(t.timestamp) >= periodStart)

  const totalVolume = periodTransactions.reduce((sum, t) => sum + t.mwk_amount, 0)
  const totalUSDT = periodTransactions.reduce((sum, t) => sum + t.usdt_amount, 0)
  const transactionCount = periodTransactions.length

  // Calculate profit (simplified - in production would be more complex)
  const totalProfit = totalVolume * 0.04 // Assuming 4% average margin
  const profitMargin = totalVolume > 0 ? (totalProfit / totalVolume) * 100 : 0
  const averageTransactionSize = transactionCount > 0 ? totalUSDT / transactionCount : 0

  const daysInPeriod = period === "daily" ? 1 : period === "weekly" ? 7 : 30
  const dailyAverage = totalVolume / daysInPeriod

  // Mock growth calculations (in production would compare to previous periods)
  const weeklyGrowth = Math.random() * 20 - 10 // -10% to +10%
  const monthlyGrowth = Math.random() * 30 - 15 // -15% to +15%

  return {
    totalVolume,
    totalProfit,
    profitMargin,
    averageTransactionSize,
    transactionCount,
    dailyAverage,
    weeklyGrowth,
    monthlyGrowth,
  }
}

// Calculate detailed profit breakdown
export const calculateProfitBreakdown = (
  totalVolume: number,
  totalUSDT: number,
  exchangeRate: number,
  feeRate = 0.001,
  taxRate = 0.15,
): ProfitBreakdown => {
  const expectedMWK = totalUSDT * exchangeRate
  const grossProfit = totalVolume - expectedMWK
  const fees = totalVolume * feeRate
  const taxes = Math.max(0, grossProfit * taxRate)
  const netProfit = grossProfit - fees - taxes

  const supplierShare = netProfit * 0.6
  const operatorShare = netProfit * 0.4

  return {
    grossProfit,
    netProfit,
    supplierShare,
    operatorShare,
    fees,
    taxes,
  }
}

// Calculate risk metrics
export const calculateRiskMetrics = (transactions: any[], currentBalance: number): RiskMetrics => {
  // Volatility based on transaction amount variance
  const amounts = transactions.map((t) => t.usdt_amount)
  const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length
  const variance = amounts.reduce((sum, amt) => sum + Math.pow(amt - avgAmount, 2), 0) / amounts.length
  const volatilityScore = Math.min(100, (Math.sqrt(variance) / avgAmount) * 100)

  // Liquidity ratio (current balance vs average daily volume)
  const dailyVolume = transactions
    .filter((t) => new Date(t.timestamp) >= new Date(Date.now() - 24 * 60 * 60 * 1000))
    .reduce((sum, t) => sum + t.usdt_amount, 0)
  const liquidityRatio = dailyVolume > 0 ? (currentBalance / dailyVolume) * 100 : 100

  // Concentration risk (largest customer as % of total volume)
  const customerVolumes: Record<string, number> = transactions.reduce(
    (acc: Record<string, number>, t: any) => {
      acc[t.counterparty] = (acc[t.counterparty] || 0) + t.usdt_amount
      return acc
    },
    {},
  )

  const totalVolume: number = Object.values(customerVolumes).reduce((sum, vol) => sum + vol, 0)
  const largestCustomer: number = Math.max(...Object.values(customerVolumes))
  const concentrationRisk = totalVolume > 0 ? (largestCustomer / totalVolume) * 100 : 0

  // Operational risk (simplified score based on transaction frequency)
  const recentTransactions = transactions.filter(
    (t) => new Date(t.timestamp) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
  ).length
  const operationalRisk = Math.max(0, 100 - recentTransactions * 2) // Lower risk with more activity

  // Overall risk score (weighted average)
  const overallRiskScore =
    volatilityScore * 0.3 + (100 - liquidityRatio) * 0.3 + concentrationRisk * 0.2 + operationalRisk * 0.2

  return {
    volatilityScore,
    liquidityRatio,
    concentrationRisk,
    operationalRisk,
    overallRiskScore,
  }
}

// Generate forecast data
export const generateForecast = (historicalData: any[], periods = 12): ForecastData[] => {
  const forecast: ForecastData[] = []

  // Simple linear regression for forecasting (in production would use more sophisticated models)
  const recentVolumes = historicalData.slice(-6).map((d) => d.totalVolume)
  const avgGrowth =
    recentVolumes.length > 1 ? (recentVolumes[recentVolumes.length - 1] - recentVolumes[0]) / recentVolumes.length : 0

  const baseVolume = recentVolumes[recentVolumes.length - 1] || 1000000

  for (let i = 1; i <= periods; i++) {
    const projectedVolume = baseVolume + avgGrowth * i
    const projectedProfit = projectedVolume * 0.04 // 4% margin
    const confidence = Math.max(50, 95 - i * 5) // Decreasing confidence over time

    forecast.push({
      period: `Month ${i}`,
      projectedVolume,
      projectedProfit,
      confidence,
    })
  }

  return forecast
}

// Currency conversion utilities
export const convertCurrency = (
  amount: number,
  fromCurrency: "USD" | "MWK" | "ZAR",
  toCurrency: "USD" | "MWK" | "ZAR",
  rates: { usd_mwk: number; usd_zar: number },
): number => {
  if (fromCurrency === toCurrency) return amount

  // Convert to USD first
  let usdAmount = amount
  if (fromCurrency === "MWK") {
    usdAmount = amount / rates.usd_mwk
  } else if (fromCurrency === "ZAR") {
    usdAmount = amount / rates.usd_zar
  }

  // Convert from USD to target currency
  if (toCurrency === "MWK") {
    return usdAmount * rates.usd_mwk
  } else if (toCurrency === "ZAR") {
    return usdAmount * rates.usd_zar
  }

  return usdAmount
}

// Performance benchmarking
export const calculatePerformanceBenchmarks = (metrics: FinancialMetrics) => {
  return {
    profitMarginGrade:
      metrics.profitMargin >= 5 ? "A" : metrics.profitMargin >= 3 ? "B" : metrics.profitMargin >= 1 ? "C" : "D",
    volumeGrowthGrade:
      metrics.monthlyGrowth >= 15 ? "A" : metrics.monthlyGrowth >= 5 ? "B" : metrics.monthlyGrowth >= 0 ? "C" : "D",
    efficiencyScore: Math.min(100, metrics.averageTransactionSize / 100 + metrics.transactionCount / 10),
    overallScore: (metrics.profitMargin * 2 + Math.max(0, metrics.monthlyGrowth) + metrics.transactionCount / 10) / 3,
  }
}