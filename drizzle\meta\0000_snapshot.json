{"id": "b493e492-c439-4494-a90a-1a74d0c642a3", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.profits": {"name": "profits", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "week_id": {"name": "week_id", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "total_usdt_received": {"name": "total_usdt_received", "type": "numeric(18, 6)", "primaryKey": false, "notNull": true}, "total_mwk_collected": {"name": "total_mwk_collected", "type": "numeric(18, 2)", "primaryKey": false, "notNull": true}, "gross_profit": {"name": "gross_profit", "type": "numeric(18, 2)", "primaryKey": false, "notNull": true}, "supplier_share": {"name": "supplier_share", "type": "numeric(18, 2)", "primaryKey": false, "notNull": true}, "operator_share": {"name": "operator_share", "type": "numeric(18, 2)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.settlements": {"name": "settlements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "amount": {"name": "amount", "type": "numeric(18, 6)", "primaryKey": false, "notNull": true}, "recipient": {"name": "recipient", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "txid": {"name": "txid", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "usdt_amount": {"name": "usdt_amount", "type": "numeric(18, 6)", "primaryKey": false, "notNull": true}, "mwk_amount": {"name": "mwk_amount", "type": "numeric(18, 2)", "primaryKey": false, "notNull": true}, "rate_used": {"name": "rate_used", "type": "numeric(10, 4)", "primaryKey": false, "notNull": true}, "txid": {"name": "txid", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "counterparty": {"name": "counterparty", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "nick_name": {"name": "nick_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "wallet_address": {"name": "wallet_address", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}