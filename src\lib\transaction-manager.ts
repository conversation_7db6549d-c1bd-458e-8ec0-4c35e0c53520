"use client"

import { FinancialCalculationsEngine } from "@/lib/financial-caluclations2"

export type TransactionStatus = "pending" | "processing" | "completed" | "failed" | "cancelled"
export type TransactionType = "buy" | "sell" | "transfer" | "refund"
export type Currency = "USDC" | "USDT" | "BUSD" | "DAI"

export interface Transaction {
  id: string
  operatorId: string
  operatorName: string
  supplierId: string
  amount: number
  currency: Currency
  type: TransactionType
  status: TransactionStatus
  fees: {
    supplierFee: number
    operatorCommission: number
    networkFee: number
    totalFees: number
  }
  exchangeRate: number
  timestamp: Date
  completedAt?: Date
  failureReason?: string
  metadata?: {
    customerInfo?: string
    location?: string
    deviceId?: string
  }
}

export interface TransactionSummary {
  totalTransactions: number
  totalVolume: number
  totalFees: number
  successRate: number
  averageTransactionSize: number
  transactionsByStatus: Record<TransactionStatus, number>
  transactionsByType: Record<TransactionType, number>
  transactionsByCurrency: Record<Currency, number>
}

class TransactionManager {
  private transactions: Transaction[] = []
  private listeners: Array<(transactions: Transaction[]) => void> = []

  constructor() {
    // Load transactions from localStorage on initialization
    this.loadTransactions()
  }

  private loadTransactions() {
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("tevavend_transactions")
      if (stored) {
        this.transactions = JSON.parse(stored).map((t: any) => ({
          ...t,
          timestamp: new Date(t.timestamp),
          completedAt: t.completedAt ? new Date(t.completedAt) : undefined,
        }))
      }
    }
  }

  private saveTransactions() {
    if (typeof window !== "undefined") {
      localStorage.setItem("tevavend_transactions", JSON.stringify(this.transactions))
    }
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => listener([...this.transactions]))
  }

  subscribe(listener: (transactions: Transaction[]) => void) {
    this.listeners.push(listener)
    // Immediately call with current data
    listener([...this.transactions])

    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener)
    }
  }

  async createTransaction(
    operatorId: string,
    operatorName: string,
    supplierId: string,
    amount: number,
    currency: Currency,
    type: TransactionType,
    exchangeRate: number,
    metadata?: Transaction["metadata"],
  ): Promise<{ success: boolean; transaction?: Transaction; error?: string }> {
    try {
      // Validate transaction
      const validation = FinancialCalculationsEngine.validateTransaction(amount, currency)
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(", ") }
      }

      // Calculate fees
      const fees = FinancialCalculationsEngine.calculateTransactionFees(amount, currency)

      // Create transaction
      const transaction: Transaction = {
        id: this.generateTransactionId(),
        operatorId,
        operatorName,
        supplierId,
        amount,
        currency,
        type,
        status: "pending",
        fees,
        exchangeRate,
        timestamp: new Date(),
        metadata,
      }

      this.transactions.unshift(transaction) // Add to beginning for latest first
      this.saveTransactions()
      this.notifyListeners()

      // Simulate processing
      setTimeout(() => this.processTransaction(transaction.id), 1000)

      return { success: true, transaction }
    } catch (error) {
      return { success: false, error: "Failed to create transaction" }
    }
  }

  private async processTransaction(transactionId: string) {
    const transaction = this.transactions.find((t) => t.id === transactionId)
    if (!transaction) return

    // Update to processing
    transaction.status = "processing"
    this.saveTransactions()
    this.notifyListeners()

    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Simulate success/failure (90% success rate)
    const isSuccess = Math.random() > 0.1

    if (isSuccess) {
      transaction.status = "completed"
      transaction.completedAt = new Date()
    } else {
      transaction.status = "failed"
      transaction.failureReason = "Network timeout - please retry"
    }

    this.saveTransactions()
    this.notifyListeners()
  }

  async retryTransaction(transactionId: string): Promise<boolean> {
    const transaction = this.transactions.find((t) => t.id === transactionId)
    if (!transaction || transaction.status !== "failed") return false

    transaction.status = "pending"
    transaction.failureReason = undefined
    this.saveTransactions()
    this.notifyListeners()

    // Reprocess
    setTimeout(() => this.processTransaction(transactionId), 1000)
    return true
  }

  async cancelTransaction(transactionId: string): Promise<boolean> {
    const transaction = this.transactions.find((t) => t.id === transactionId)
    if (!transaction || !["pending", "processing"].includes(transaction.status)) return false

    transaction.status = "cancelled"
    this.saveTransactions()
    this.notifyListeners()
    return true
  }

  getTransactions(filters?: {
    operatorId?: string
    supplierId?: string
    status?: TransactionStatus
    type?: TransactionType
    currency?: Currency
    dateFrom?: Date
    dateTo?: Date
    limit?: number
  }): Transaction[] {
    let filtered = [...this.transactions]

    if (filters) {
      if (filters.operatorId) {
        filtered = filtered.filter((t) => t.operatorId === filters.operatorId)
      }
      if (filters.supplierId) {
        filtered = filtered.filter((t) => t.supplierId === filters.supplierId)
      }
      if (filters.status) {
        filtered = filtered.filter((t) => t.status === filters.status)
      }
      if (filters.type) {
        filtered = filtered.filter((t) => t.type === filters.type)
      }
      if (filters.currency) {
        filtered = filtered.filter((t) => t.currency === filters.currency)
      }
      if (filters.dateFrom) {
        filtered = filtered.filter((t) => t.timestamp >= filters.dateFrom!)
      }
      if (filters.dateTo) {
        filtered = filtered.filter((t) => t.timestamp <= filters.dateTo!)
      }
      if (filters.limit) {
        filtered = filtered.slice(0, filters.limit)
      }
    }

    return filtered
  }

  getTransactionSummary(filters?: {
    operatorId?: string
    supplierId?: string
    dateFrom?: Date
    dateTo?: Date
  }): TransactionSummary {
    const transactions = this.getTransactions(filters)

    const totalTransactions = transactions.length
    const totalVolume = transactions.reduce((sum, t) => sum + t.amount, 0)
    const totalFees = transactions.reduce((sum, t) => sum + t.fees.totalFees, 0)
    const completedTransactions = transactions.filter((t) => t.status === "completed").length
    const successRate = totalTransactions > 0 ? (completedTransactions / totalTransactions) * 100 : 0
    const averageTransactionSize = totalTransactions > 0 ? totalVolume / totalTransactions : 0

    const transactionsByStatus: Record<TransactionStatus, number> = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
    }

    const transactionsByType: Record<TransactionType, number> = {
      buy: 0,
      sell: 0,
      transfer: 0,
      refund: 0,
    }

    const transactionsByCurrency: Record<Currency, number> = {
      USDC: 0,
      USDT: 0,
      BUSD: 0,
      DAI: 0,
    }

    transactions.forEach((t) => {
      transactionsByStatus[t.status]++
      transactionsByType[t.type]++
      transactionsByCurrency[t.currency]++
    })

    return {
      totalTransactions,
      totalVolume: Number(totalVolume.toFixed(2)),
      totalFees: Number(totalFees.toFixed(2)),
      successRate: Number(successRate.toFixed(1)),
      averageTransactionSize: Number(averageTransactionSize.toFixed(2)),
      transactionsByStatus,
      transactionsByType,
      transactionsByCurrency,
    }
  }

  private generateTransactionId(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 5)
    return `TX${timestamp}${random}`.toUpperCase()
  }

  // Seed with some initial data for demo
  seedDemoData() {
    if (this.transactions.length > 0) return // Don't seed if data already exists

    const demoTransactions: Omit<Transaction, "id">[] = [
      {
        operatorId: "2",
        operatorName: "Jane Operator",
        supplierId: "1",
        amount: 500,
        currency: "USDC",
        type: "buy",
        status: "completed",
        fees: FinancialCalculationsEngine.calculateTransactionFees(500, "USDC"),
        exchangeRate: 1.0,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000),
        metadata: { location: "Downtown Branch" },
      },
      {
        operatorId: "2",
        operatorName: "Jane Operator",
        supplierId: "1",
        amount: 250,
        currency: "USDT",
        type: "sell",
        status: "completed",
        fees: FinancialCalculationsEngine.calculateTransactionFees(250, "USDT"),
        exchangeRate: 1.0,
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        completedAt: new Date(Date.now() - 4 * 60 * 60 * 1000 + 45000),
        metadata: { location: "Mall Kiosk" },
      },
      {
        operatorId: "2",
        operatorName: "Jane Operator",
        supplierId: "1",
        amount: 100,
        currency: "USDC",
        type: "buy",
        status: "failed",
        fees: FinancialCalculationsEngine.calculateTransactionFees(100, "USDC"),
        exchangeRate: 1.0,
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
        failureReason: "Insufficient liquidity",
        metadata: { location: "Airport Terminal" },
      },
    ]

    demoTransactions.forEach((t) => {
      this.transactions.push({
        ...t,
        id: this.generateTransactionId(),
      })
    })

    this.saveTransactions()
    this.notifyListeners()
  }
}

// Export singleton instance
export const transactionManager = new TransactionManager()

export default transactionManager