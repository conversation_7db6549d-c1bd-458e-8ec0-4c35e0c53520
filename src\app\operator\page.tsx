"use client"

import OperatorDashboard from "@/components/operator-dashboard"
import { useAuth } from "@/lib/auth"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function OperatorPage() {
    const { user, isLoading } = useAuth()
    const router = useRouter()

    useEffect(() => {
        if (!isLoading) {
            if (!user) {
                router.push("/login")
            } else if (user.role !== "operator") {
                if (user.role === "supplier") {
                    router.push("/supplier")
                } else {
                    router.push("/login")
                }
            }
        }
    }, [user, isLoading, router])

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-secondary/5">
                <div className="text-center">
                    <div className="w-16 h-16 bg-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                        <div className="text-2xl font-bold text-primary-foreground">TV</div>
                    </div>
                    <p className="text-muted-foreground">Loading TEVAVEND...</p>
                </div>
            </div>
        )
    }

    if (!user || user.role !== "operator") {
        return null // Will redirect via useEffect
    }

    return <OperatorDashboard />
}
