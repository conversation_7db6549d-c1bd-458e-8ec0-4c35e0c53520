"use client"

import { useAuth } from "@/lib/auth"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, User, Building2 } from "lucide-react"

export function Navigation() {
  const { user, logout } = useAuth()
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push("/login")
  }

  if (!user) return null

  return (
    <nav className="bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
            <div className="text-lg font-bold text-primary-foreground">TV</div>
          </div>
          <div>
            <h1 className="text-xl font-semibold text-foreground">TEVAVEND</h1>
            <p className="text-sm text-muted-foreground">
              {user.role === "supplier" ? "Supplier Portal" : "Operator Portal"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {user.role === "supplier" ? <Building2 className="w-4 h-4" /> : <User className="w-4 h-4" />}
            <span>{user.name}</span>
            <span className="text-xs bg-secondary px-2 py-1 rounded-full capitalize">{user.role}</span>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleLogout}
            className="text-muted-foreground hover:text-foreground"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </Button>
        </div>
      </div>
    </nav>
  )
}

export default Navigation