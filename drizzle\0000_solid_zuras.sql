CREATE TABLE IF NOT EXISTS "profits" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"week_id" varchar(20) NOT NULL,
	"total_usdt_received" numeric(18, 6) NOT NULL,
	"total_mwk_collected" numeric(18, 2) NOT NULL,
	"gross_profit" numeric(18, 2) NOT NULL,
	"supplier_share" numeric(18, 2) NOT NULL,
	"operator_share" numeric(18, 2) NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"user_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "settlements" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"amount" numeric(18, 6) NOT NULL,
	"recipient" varchar(100) NOT NULL,
	"status" varchar(20) DEFAULT 'pending',
	"txid" varchar(100),
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"processed_at" timestamp,
	"user_id" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "transactions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"type" varchar(50) NOT NULL,
	"usdt_amount" numeric(18, 6) NOT NULL,
	"mwk_amount" numeric(18, 2) NOT NULL,
	"rate_used" numeric(10, 4) NOT NULL,
	"txid" varchar(100),
	"counterparty" varchar(255) NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"user_id" uuid NOT NULL,
	"status" varchar(20) DEFAULT 'pending'
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL,
	"nick_name" varchar(100),
	"role" varchar(20) NOT NULL,
	"wallet_address" varchar(100),
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);

