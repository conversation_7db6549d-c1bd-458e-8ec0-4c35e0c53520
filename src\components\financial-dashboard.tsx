"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DollarSign, TrendingUp, TrendingDown, AlertTriangle, Target } from "lucide-react"
import {
    calculateFinancialMetrics,
    calculateProfitBreakdown,
    calculateRiskMetrics,
    generateForecast,
    calculatePerformanceBenchmarks,
    convertCurrency,
    type FinancialMetrics,
    type ProfitBreakdown,
    type RiskMetrics,
    type ForecastData,
} from "@/lib/financial-calculations"
import { getTransactions, getExchangeRates } from "@/lib/auth"

interface FinancialDashboardProps {
    userRole: string
    userId: string
}

export function FinancialDashboard({ userRole, userId }: FinancialDashboardProps) {
    const [period, setPeriod] = useState("30d")
    const [loading, setLoading] = useState(true)
    const [metrics, setMetrics] = useState<FinancialMetrics | null>(null)
    const [profitBreakdown, setProfitBreakdown] = useState<ProfitBreakdown | null>(null)
    const [riskMetrics, setRiskMetrics] = useState<RiskMetrics | null>(null)
    const [forecast, setForecast] = useState<ForecastData[]>([])
    const [transactions, setTransactions] = useState<any[]>([])
    const [rates, setRates] = useState({ usd_mwk: 1750, black_market_rate: 1800, usd_zar: 18.5 })

    useEffect(() => {
        const loadData = async () => {
            try {
                setLoading(true)
                const [transactionsData, ratesData] = await Promise.all([
                    getTransactions(),
                    getExchangeRates()
                ])
                
                setTransactions(transactionsData || [])
                setRates(ratesData || rates)
            } catch (error) {
                console.error("Error loading financial data:", error)
            } finally {
                setLoading(false)
            }
        }

        loadData()
    }, [])

    useEffect(() => {
        if (!transactions.length) return

        const calculateMetrics = () => {
            setLoading(true)

            const financialMetrics = calculateFinancialMetrics(transactions, period)
            const breakdown = calculateProfitBreakdown(
                financialMetrics.totalVolume,
                financialMetrics.totalVolume / rates.usd_mwk,
                rates.usd_mwk,
            )
            const risk = calculateRiskMetrics(transactions, 2450)
            const forecastData = generateForecast([financialMetrics], 6)

            setMetrics(financialMetrics)
            setProfitBreakdown(breakdown)
            setRiskMetrics(risk)
            setForecast(forecastData)
            setLoading(false)
        }

        calculateMetrics()
    }, [period, transactions, rates])

    if (loading || !metrics || !profitBreakdown || !riskMetrics) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-2">Loading financial data...</p>
                </div>
            </div>
        )
    }

    const benchmarks = calculatePerformanceBenchmarks(metrics)

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Financial Analytics</h2>
                    <p className="text-muted-foreground">Comprehensive financial performance analysis</p>
                </div>
                <Select value={period} onValueChange={setPeriod}>
                    <SelectTrigger className="w-32">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="7d">7 Days</SelectItem>
                        <SelectItem value="30d">30 Days</SelectItem>
                        <SelectItem value="90d">90 Days</SelectItem>
                        <SelectItem value="1y">1 Year</SelectItem>
                    </SelectContent>
                </Select>
            </div>

            <Tabs defaultValue="overview" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="profitability">Profitability</TabsTrigger>
                    <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
                    <TabsTrigger value="forecast">Forecast</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Volume</CardTitle>
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">MWK {metrics.totalVolume.toLocaleString()}</div>
                                <div className="flex items-center space-x-1 mt-1">
                                    {metrics.weeklyGrowth >= 0 ? (
                                        <TrendingUp className="h-3 w-3 text-chart-1" />
                                    ) : (
                                        <TrendingDown className="h-3 w-3 text-destructive" />
                                    )}
                                    <span className={`text-xs ${metrics.weeklyGrowth >= 0 ? 'text-chart-1' : 'text-destructive'}`}>
                                        {metrics.weeklyGrowth.toFixed(1)}%
                                    </span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
                                <Target className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{metrics.profitMargin.toFixed(2)}%</div>
                                <Progress value={metrics.profitMargin} className="mt-2" />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{riskMetrics.overallRiskScore.toFixed(1)}</div>
                                <Progress value={riskMetrics.overallRiskScore} className="mt-2" />
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Transactions</CardTitle>
                                <Target className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{metrics.transactionCount}</div>
                                <p className="text-xs text-muted-foreground">
                                    Avg: MWK {metrics.averageTransactionSize.toLocaleString()}
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="forecast" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>6-Month Forecast</CardTitle>
                            <CardDescription>Projected volume and profit based on historical trends</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {forecast.map((item, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-2 h-2 rounded-full bg-chart-1" />
                                            <span className="font-medium">{item.period}</span>
                                        </div>
                                        <div className="text-right space-y-1">
                                            <div className="text-sm font-mono">MWK {item.projectedVolume.toLocaleString()}</div>
                                            <div className="text-xs text-muted-foreground">
                                                Profit: MWK {item.projectedProfit.toLocaleString()}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
}
