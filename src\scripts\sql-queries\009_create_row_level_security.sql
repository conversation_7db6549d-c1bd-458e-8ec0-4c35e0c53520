-- Created Row Level Security policies for data protection
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_rates ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE operator_locations ENABLE ROW LEVEL SECURITY;
-- profits table RLS will be enabled after creation

-- Users table policies
CREATE POLICY users_select_own ON users
    FOR SELECT
    USING (auth.uid()::text = id::text);

CREATE POLICY users_insert_self ON users
    FOR INSERT
    WITH CHECK (auth.uid()::text = id::text);

CREATE POLICY users_update_own ON users
    FOR UPDATE
    USING (auth.uid()::text = id::text);

-- Transactions table policies
CREATE POLICY transactions_select_own ON transactions
    FOR SELECT
    USING (
        auth.uid()::text = operator_id::text OR 
        auth.uid()::text = supplier_id::text
    );

CREATE POLICY transactions_insert_operator ON transactions
    FOR INSERT
    WITH CHECK (auth.uid()::text = operator_id::text);

CREATE POLICY transactions_update_own ON transactions
    FOR UPDATE
    USING (
        auth.uid()::text = operator_id::text OR 
        auth.uid()::text = supplier_id::text
    );

-- Exchange rates table policies (read-only for all authenticated users)
CREATE POLICY exchange_rates_select_all ON exchange_rates
    FOR SELECT
    USING (auth.role() = 'authenticated');

-- Financial metrics table policies
CREATE POLICY financial_metrics_select_own ON financial_metrics
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

CREATE POLICY financial_metrics_insert_own ON financial_metrics
    FOR INSERT
    WITH CHECK (auth.uid()::text = user_id::text);

-- Audit logs table policies (read-only for own records)
CREATE POLICY audit_logs_select_own ON audit_logs
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

-- Compliance records table policies
CREATE POLICY compliance_records_select_own ON compliance_records
    FOR SELECT
    USING (auth.uid()::text = user_id::text);

-- Operator locations table policies
CREATE POLICY operator_locations_select_own ON operator_locations
    FOR SELECT
    USING (auth.uid()::text = operator_id::text);

CREATE POLICY operator_locations_insert_own ON operator_locations
    FOR INSERT
    WITH CHECK (auth.uid()::text = operator_id::text);

CREATE POLICY operator_locations_update_own ON operator_locations
    FOR UPDATE
    USING (auth.uid()::text = operator_id::text);

CREATE POLICY operator_locations_delete_own ON operator_locations
    FOR DELETE
    USING (auth.uid()::text = operator_id::text);

-- Profits table policies
-- Assumes a profits table with user_id column exists
CREATE POLICY profits_select_own ON profits
    FOR SELECT
    USING (auth.uid()::text = user_id::text);
CREATE POLICY profits_insert_own ON profits
    FOR INSERT
    WITH CHECK (auth.uid()::text = user_id::text);
